import Database from 'better-sqlite3';

try {
  const db = new Database('data.db');
  
  console.log('=== EMAIL TEMPLATES TABLE STRUCTURE ===');
  const tableInfo = db.prepare("PRAGMA table_info(email_templates)").all();
  console.log(tableInfo);
  
  console.log('\n=== EMAIL TEMPLATES DATA ===');
  const templates = db.prepare("SELECT * FROM email_templates LIMIT 5").all();
  console.log('Number of templates:', templates.length);
  
  if (templates.length > 0) {
    console.log('First template:');
    console.log(JSON.stringify(templates[0], null, 2));
    
    // Check data types
    console.log('\n=== DATA TYPE ANALYSIS ===');
    const firstTemplate = templates[0];
    Object.keys(firstTemplate).forEach(key => {
      const value = firstTemplate[key];
      console.log(`${key}: ${typeof value} = ${value}`);
    });
  }
  
  db.close();
  console.log('\n=== DONE ===');
} catch (error) {
  console.error('Error:', error);
}
