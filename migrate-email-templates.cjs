const Database = require('better-sqlite3');
const path = require('path');

// Initialize SQLite database
const dbPath = path.join(__dirname, 'data.db');
console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  console.log('Checking if email_templates table exists...');
  
  // Check if table exists
  const tableExists = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='email_templates'
  `).get();
  
  if (tableExists) {
    console.log('email_templates table already exists');
    
    // Check table structure
    const tableInfo = db.prepare("PRAGMA table_info(email_templates)").all();
    console.log('Current table structure:', tableInfo.map(col => `${col.name} (${col.type})`).join(', '));
    
    // Check if all required columns exist
    const requiredColumns = [
      'id', 'template_id', 'name', 'description', 'subject', 
      'html_content', 'text_content', 'content', 'category', 
      'is_default', 'created_at', 'updated_at'
    ];
    
    const existingColumns = tableInfo.map(col => col.name);
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log('Missing columns:', missingColumns);
      
      // Add missing columns
      for (const column of missingColumns) {
        let columnDef = '';
        switch (column) {
          case 'template_id':
            columnDef = 'template_id TEXT NOT NULL UNIQUE';
            break;
          case 'name':
            columnDef = 'name TEXT NOT NULL';
            break;
          case 'description':
            columnDef = 'description TEXT';
            break;
          case 'subject':
            columnDef = 'subject TEXT NOT NULL';
            break;
          case 'html_content':
            columnDef = 'html_content TEXT NOT NULL';
            break;
          case 'text_content':
            columnDef = 'text_content TEXT';
            break;
          case 'content':
            columnDef = 'content TEXT';
            break;
          case 'category':
            columnDef = 'category TEXT NOT NULL DEFAULT "general"';
            break;
          case 'is_default':
            columnDef = 'is_default INTEGER NOT NULL DEFAULT 0';
            break;
          case 'created_at':
            columnDef = 'created_at TEXT NOT NULL';
            break;
          case 'updated_at':
            columnDef = 'updated_at TEXT NOT NULL';
            break;
          default:
            columnDef = `${column} TEXT`;
        }
        
        try {
          db.exec(`ALTER TABLE email_templates ADD COLUMN ${columnDef}`);
          console.log(`Added column: ${column}`);
        } catch (error) {
          console.log(`Column ${column} might already exist or error:`, error.message);
        }
      }
    } else {
      console.log('All required columns exist');
    }
  } else {
    console.log('Creating email_templates table...');
    
    // Create the table
    db.exec(`
      CREATE TABLE email_templates (
        id INTEGER PRIMARY KEY,
        template_id TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        subject TEXT NOT NULL,
        html_content TEXT NOT NULL,
        text_content TEXT,
        content TEXT,
        category TEXT NOT NULL DEFAULT 'general',
        is_default INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    
    console.log('Successfully created email_templates table');
  }
  
  // Test inserting a sample template
  console.log('Testing template insertion...');
  
  const testTemplate = {
    template_id: `test_${Date.now()}`,
    name: 'Test Template',
    description: 'Test template for migration',
    subject: 'Test Subject',
    html_content: '<p>Test content</p>',
    text_content: 'Test content',
    content: 'Test content',
    category: 'general',
    is_default: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  try {
    const insertStmt = db.prepare(`
      INSERT INTO email_templates (
        template_id, name, description, subject, html_content, 
        text_content, content, category, is_default, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const result = insertStmt.run(
      testTemplate.template_id,
      testTemplate.name,
      testTemplate.description,
      testTemplate.subject,
      testTemplate.html_content,
      testTemplate.text_content,
      testTemplate.content,
      testTemplate.category,
      testTemplate.is_default,
      testTemplate.created_at,
      testTemplate.updated_at
    );
    
    console.log('Test template inserted successfully, ID:', result.lastInsertRowid);
    
    // Clean up test template
    db.prepare('DELETE FROM email_templates WHERE template_id = ?').run(testTemplate.template_id);
    console.log('Test template cleaned up');
    
  } catch (error) {
    console.error('Error testing template insertion:', error);
  }
  
  db.close();
  console.log('Migration completed successfully');
} catch (error) {
  console.error('Migration failed:', error);
  process.exit(1);
}
