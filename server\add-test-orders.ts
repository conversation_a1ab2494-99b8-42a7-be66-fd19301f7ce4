import { storage } from './storage-factory';

/**
 * Create test orders directly in the database
 */
export async function addTestOrders() {
  try {
    console.log('Creating test orders directly...');

    // Create a regular order
    console.log('Creating regular order...');

    // Get the regular checkout page
    const regularCheckoutPages = await storage.getCustomCheckoutPages();
    const regularCheckoutPage = regularCheckoutPages.find(page => !page.isTrialCheckout);

    const regularOrder = await storage.createInvoice({
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '19.99',
      status: 'paid',
      country: 'United States',
      appType: 'IPTV Smarters Pro',
      createdAt: new Date().toISOString(),
      isTrialOrder: false,
      hasUpgraded: false,
      notes: 'Default regular order customer',
      customCheckoutPageId: regularCheckoutPage ? regularCheckoutPage.id : undefined
    });

    console.log('Regular order created:', regularOrder);

    // Create a trial order
    console.log('Creating trial order...');

    // Get the trial checkout page
    const trialCheckoutPages = await storage.getCustomCheckoutPages();
    const trialCheckoutPage = trialCheckoutPages.find(page => page.isTrialCheckout);

    const trialOrder = await storage.createInvoice({
      customerName: 'Jane Doe',
      customerEmail: '<EMAIL>',
      productId: 1,
      amount: '4.99',
      status: 'paid',
      country: 'United Kingdom',
      appType: 'MAG',
      macAddress: '00:1A:79:B4:E7:2D',
      createdAt: new Date().toISOString(),
      isTrialOrder: true,
      hasUpgraded: false,
      notes: 'Default trial order customer',
      customCheckoutPageId: trialCheckoutPage ? trialCheckoutPage.id : undefined
    });

    console.log('Trial order created:', trialOrder);

    // Create a regular order for the same customer as the trial order
    console.log('Creating regular order for trial customer...');

    const upgradeOrder = await storage.createInvoice({
      customerName: 'Jane Doe',
      customerEmail: '<EMAIL>', // Same email as trial order
      productId: 1,
      amount: '19.99',
      status: 'sent', // Start as sent, so we can test the upgrade feature
      country: 'United Kingdom',
      appType: 'IPTV Smarters Pro',
      createdAt: new Date().toISOString(),
      isTrialOrder: false,
      hasUpgraded: false,
      notes: 'Regular order for trial customer',
      customCheckoutPageId: regularCheckoutPage ? regularCheckoutPage.id : undefined
    });

    console.log('Regular order for trial customer created:', upgradeOrder);

    console.log('Test orders created successfully!');
    console.log('\nRegular Order Details:');
    console.log('- Customer: John Smith (<EMAIL>)');
    console.log('- Country: United States');
    console.log('- Application: IPTV Smarters Pro');
    console.log('- Amount: $19.99');
    console.log('- Status: paid');

    console.log('\nTrial Order Details:');
    console.log('- Customer: Jane Doe (<EMAIL>)');
    console.log('- Country: United Kingdom');
    console.log('- Application: MAG');
    console.log('- MAC Address: 00:1A:79:B4:E7:2D');
    console.log('- Amount: $4.99');
    console.log('- Status: paid');

    console.log('\nUpgrade Order Details:');
    console.log('- Customer: Jane Doe (<EMAIL>)');
    console.log('- Country: United Kingdom');
    console.log('- Application: IPTV Smarters Pro');
    console.log('- Amount: $19.99');
    console.log('- Status: sent');

    return { regularOrder, trialOrder, upgradeOrder };
  } catch (error) {
    console.error('Error creating test orders:', error);
    throw error;
  }
}
