import { db } from './db';
import { eq } from 'drizzle-orm';
import {
  users,
  products,
  invoices,
  customCheckoutPages,
  allowedEmails,
  emailTemplates,
  paypalButtons,
  customInvoices,
  type User,
  type InsertUser,
  type Product,
  type InsertProduct,
  type Invoice,
  type InsertInvoice,
  type CustomCheckoutPage,
  type InsertCustomCheckoutPage,
  type AllowedEmail,
  type InsertAllowedEmail,
  type EmailTemplate,
  type InsertEmailTemplate,
  type PaypalButton,
  type InsertPaypalButton,
  type CustomInvoice,
  type InsertCustomInvoice
} from '@shared/schema';
import { EmbedCode } from '@shared/embed-codes';
import { IStorage } from './storage';
import { createHash } from 'crypto';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
      const user = result[0];
      if (!user) return undefined;
      
      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: undefined,
        twoFactorEnabled: false,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error getting user:', error);
      return undefined;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
      const user = result[0];
      if (!user) return undefined;
      
      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: undefined,
        twoFactorEnabled: false,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error getting user by username:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      let password = insertUser.password;
      if (!password.match(/^[0-9a-f]{64}$/i)) {
        password = createHash('sha256').update(password).digest('hex');
      }

      const userData = { ...insertUser, password };
      const result = await db.insert(users).values(userData).returning();
      const user = result[0];
      
      return {
        ...user,
        rememberMe: false,
        resetToken: undefined,
        resetTokenExpiry: undefined,
        twoFactorSecret: undefined,
        twoFactorEnabled: false,
        recoveryCodes: [],
        devices: []
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async verifyUserCredentials(username: string, password: string): Promise<boolean> {
    const user = await this.getUserByUsername(username);
    if (!user) return false;
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  async verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean> {
    if (!user) return false;
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return user.password === hashedPassword;
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    try {
      return await db.select().from(products);
    } catch (error) {
      console.error('Error getting products:', error);
      return [];
    }
  }

  async getProduct(id: number): Promise<Product | undefined> {
    try {
      const result = await db.select().from(products).where(eq(products.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting product:', error);
      return undefined;
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertProduct,
        // Convert boolean to integer for SQLite
        active: insertProduct.active ? 1 : 0,
        // Ensure price is a string
        price: typeof insertProduct.price === 'number' ? insertProduct.price.toString() : insertProduct.price
      };

      const result = await db.insert(products).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  // Invoice methods
  async createInvoice(insertInvoice: InsertInvoice): Promise<Invoice> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertInvoice,
        // Convert booleans to integers for SQLite
        isTrialOrder: insertInvoice.isTrialOrder ? 1 : 0,
        hasUpgraded: insertInvoice.hasUpgraded ? 1 : 0,
        // Ensure amount is a string
        amount: typeof insertInvoice.amount === 'number' ? insertInvoice.amount.toString() : insertInvoice.amount
      };

      const result = await db.insert(invoices).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }
  }

  async getInvoice(id: number): Promise<Invoice | undefined> {
    try {
      const result = await db.select().from(invoices).where(eq(invoices.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting invoice:', error);
      return undefined;
    }
  }

  async getInvoices(): Promise<Invoice[]> {
    try {
      return await db.select().from(invoices);
    } catch (error) {
      console.error('Error getting invoices:', error);
      return [];
    }
  }

  async updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined> {
    try {
      const result = await db.update(invoices).set(update).where(eq(invoices.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating invoice:', error);
      return undefined;
    }
  }

  // Custom Checkout Page methods
  async createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...insertPage,
        // Convert booleans to integers for SQLite
        requireAllowedEmail: insertPage.requireAllowedEmail ? 1 : 0,
        isTrialCheckout: insertPage.isTrialCheckout ? 1 : 0,
        useReferrerMasking: insertPage.useReferrerMasking ? 1 : 0,
        active: insertPage.active ? 1 : 0,
        // Ensure price is a string
        price: typeof insertPage.price === 'number' ? insertPage.price.toString() : insertPage.price,
        // Ensure views and conversions are numbers
        views: insertPage.views || 0,
        conversions: insertPage.conversions || 0
      };

      const result = await db.insert(customCheckoutPages).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating custom checkout page:', error);
      throw error;
    }
  }

  async getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await db.select().from(customCheckoutPages).where(eq(customCheckoutPages.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined> {
    try {
      const result = await db.select().from(customCheckoutPages).where(eq(customCheckoutPages.slug, slug)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting custom checkout page by slug:', error);
      return undefined;
    }
  }

  async getCustomCheckoutPages(): Promise<CustomCheckoutPage[]> {
    try {
      return await db.select().from(customCheckoutPages);
    } catch (error) {
      console.error('Error getting custom checkout pages:', error);
      return [];
    }
  }

  async updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined> {
    try {
      // Convert data for SQLite compatibility
      const sqliteUpdate: any = { ...update };

      // Convert booleans to integers for SQLite
      if ('requireAllowedEmail' in update) {
        sqliteUpdate.requireAllowedEmail = update.requireAllowedEmail ? 1 : 0;
      }
      if ('isTrialCheckout' in update) {
        sqliteUpdate.isTrialCheckout = update.isTrialCheckout ? 1 : 0;
      }
      if ('useReferrerMasking' in update) {
        sqliteUpdate.useReferrerMasking = update.useReferrerMasking ? 1 : 0;
      }
      if ('active' in update) {
        sqliteUpdate.active = update.active ? 1 : 0;
      }
      // Ensure price is a string
      if ('price' in update && typeof update.price === 'number') {
        sqliteUpdate.price = update.price.toString();
      }

      const result = await db.update(customCheckoutPages).set(sqliteUpdate).where(eq(customCheckoutPages.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating custom checkout page:', error);
      return undefined;
    }
  }

  async incrementCustomCheckoutPageViews(id: number): Promise<void> {
    try {
      const current = await this.getCustomCheckoutPage(id);
      if (current) {
        await db.update(customCheckoutPages)
          .set({ views: (current.views || 0) + 1 })
          .where(eq(customCheckoutPages.id, id));
      }
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  }

  async incrementCustomCheckoutPageConversions(id: number): Promise<void> {
    try {
      const current = await this.getCustomCheckoutPage(id);
      if (current) {
        await db.update(customCheckoutPages)
          .set({ conversions: (current.conversions || 0) + 1 })
          .where(eq(customCheckoutPages.id, id));
      }
    } catch (error) {
      console.error('Error incrementing conversions:', error);
    }
  }

  async deleteCustomCheckoutPage(id: number): Promise<boolean> {
    try {
      const result = await db.delete(customCheckoutPages).where(eq(customCheckoutPages.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting custom checkout page:', error);
      return false;
    }
  }

  // Allowed Email methods
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      return await db.select().from(allowedEmails);
    } catch (error) {
      console.error('Error getting allowed emails:', error);
      return [];
    }
  }

  async getAllowedEmail(id: number): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting allowed email:', error);
      return undefined;
    }
  }

  async getEmailByAddress(email: string): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.email, email.toLowerCase())).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting email by address:', error);
      return undefined;
    }
  }

  async isEmailAllowed(email: string): Promise<boolean> {
    try {
      const result = await db.select().from(allowedEmails).where(eq(allowedEmails.email, email.toLowerCase())).limit(1);
      return result.length > 0;
    } catch (error) {
      console.error('Error checking if email is allowed:', error);
      return false;
    }
  }

  async createAllowedEmail(insertAllowedEmail: InsertAllowedEmail): Promise<AllowedEmail> {
    try {
      const result = await db.insert(allowedEmails).values(insertAllowedEmail).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating allowed email:', error);
      throw error;
    }
  }

  async updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined> {
    try {
      const result = await db.update(allowedEmails).set(update).where(eq(allowedEmails.id, id)).returning();
      return result[0];
    } catch (error) {
      console.error('Error updating allowed email:', error);
      return undefined;
    }
  }

  async updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail> {
    try {
      const existing = await this.getEmailByAddress(emailAddress);
      if (existing) {
        const updated = await this.updateAllowedEmail(existing.id, update);
        return updated!;
      } else {
        return await this.createAllowedEmail({
          email: emailAddress,
          notes: update.notes || '',
          lastSubject: update.lastSubject,
          smtpProvider: update.smtpProvider,
          lastUpdated: update.lastUpdated || new Date().toISOString(),
          createdAt: update.createdAt || new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error updating or creating allowed email:', error);
      throw error;
    }
  }

  async deleteAllowedEmail(id: number): Promise<boolean> {
    try {
      const result = await db.delete(allowedEmails).where(eq(allowedEmails.id, id));
      return result.rowsAffected > 0;
    } catch (error) {
      console.error('Error deleting allowed email:', error);
      return false;
    }
  }

  async bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const email of emails) {
      if (!email.trim()) {
        failed++;
        continue;
      }

      try {
        const exists = await this.isEmailAllowed(email.trim());
        if (exists) {
          failed++;
          continue;
        }

        await this.createAllowedEmail({
          email: email.trim(),
          notes: "Bulk imported",
          lastSubject: "",
          smtpProvider: "",
          lastUpdated: new Date().toISOString(),
          createdAt: new Date().toISOString()
        });
        success++;
      } catch (error) {
        failed++;
      }
    }

    return { success, failed };
  }

  // Placeholder methods for features not yet implemented
  async saveResetToken(userId: number, token: string, expiry: Date): Promise<void> {}
  async validateResetToken(token: string): Promise<boolean> { return false; }
  async getUserByResetToken(token: string): Promise<User | undefined> { return undefined; }
  async updateUserPassword(userId: number, password: string): Promise<void> {}
  async clearResetToken(userId: number): Promise<void> {}
  async updateUsername(userId: number, username: string): Promise<void> {}
  async updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void> {}
  async enableTwoFactor(userId: number, secret: string): Promise<void> {}
  async disableTwoFactor(userId: number): Promise<void> {}
  async verifyTwoFactorToken(userId: number, token: string): Promise<boolean> { return false; }
  async generateRecoveryCodes(userId: number): Promise<string[]> { return []; }
  async verifyRecoveryCode(userId: number, code: string): Promise<boolean> { return false; }
  async addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device> {
    return { id: '', name: '', ip: '', userAgent: '', lastLogin: '', createdAt: '' };
  }
  async getDevices(userId: number): Promise<Device[]> { return []; }
  async updateDeviceLastLogin(userId: number, deviceId: string): Promise<void> {}
  async removeDevice(userId: number, deviceId: string): Promise<boolean> { return false; }

  // Configuration methods
  async getGeneralSettings(): Promise<any> {
    return {
      siteName: "TemplateHub Pro",
      siteDescription: "Premium productivity app templates and UI/UX design systems",
      logoUrl: "",
      faviconUrl: "",
      primaryColor: "#6366f1",
      secondaryColor: "#4f46e5",
      footerText: "© 2024 TemplateHub Pro",
      enableCheckout: true,
      enableCustomCheckout: true,
      enableTestMode: true,
      defaultTestCustomer: {
        enabled: true,
        name: "Test Designer",
        email: "<EMAIL>"
      },
      emailDomainRestriction: {
        enabled: false,
        allowedDomains: "gmail.com, hotmail.com, yahoo.com"
      }
    };
  }

  async getEmailConfig(): Promise<any> {
    return {
      providers: [
        {
          id: 'smtp-1',
          name: 'Primary SMTP',
          active: true,
          isDefault: true,
          isBackup: false,
          credentials: {
            host: 'smtp-relay.brevo.com',
            port: '587',
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: '********'
            },
            fromEmail: '<EMAIL>',
            fromName: 'PayPal Invoicer'
          }
        }
      ]
    };
  }

  async getPaymentConfig(): Promise<any> {
    return {
      providers: [
        {
          id: 'paypal',
          name: 'PayPal',
          active: true,
          config: {
            clientId: '********',
            clientSecret: '********',
            mode: 'sandbox',
            webhookId: '',
            paypalEmail: '<EMAIL>'
          }
        },
        {
          id: 'custom-link',
          name: 'Custom Payment Links',
          active: true,
          config: {
            links: [
              {
                id: 'link-1',
                name: 'Default Payment Required',
                paymentLink: 'https://example.com/pay',
                buttonText: 'Complete Payment',
                successRedirectUrl: 'https://example.com/thank-you',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: 0
          }
        }
      ]
    };
  }

  // Placeholder methods for other entities
  async getEmailTemplates(): Promise<EmailTemplate[]> { return []; }
  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> { return undefined; }
  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> { throw new Error('Not implemented'); }
  async updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined> { return undefined; }
  async deleteEmailTemplate(id: number): Promise<boolean> { return false; }

  async getPaypalButtons(): Promise<PaypalButton[]> {
    try {
      return await db.select().from(paypalButtons);
    } catch (error) {
      console.error('Error getting paypal buttons:', error);
      return [];
    }
  }

  async getPaypalButton(id: number): Promise<PaypalButton | undefined> {
    try {
      const result = await db.select().from(paypalButtons).where(eq(paypalButtons.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('Error getting paypal button:', error);
      return undefined;
    }
  }
  async createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton> {
    try {
      // Convert data for SQLite compatibility
      const sqliteData = {
        ...button,
        // Convert boolean to integer for SQLite
        active: button.active ? 1 : 0
      };

      const result = await db.insert(paypalButtons).values(sqliteData).returning();
      return result[0];
    } catch (error) {
      console.error('Error creating paypal button:', error);
      throw error;
    }
  }
  async updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined> { return undefined; }
  async deletePaypalButton(id: number): Promise<boolean> { return false; }

  async getCustomInvoices(): Promise<CustomInvoice[]> { return []; }
  async getCustomInvoice(id: number): Promise<CustomInvoice | undefined> { return undefined; }
  async getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined> { return undefined; }
  async createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice> { throw new Error('Not implemented'); }
  async updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined> { return undefined; }
  async incrementCustomInvoiceViewCount(id: number): Promise<void> {}
  async markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined> { return undefined; }
  async deleteCustomInvoice(id: number): Promise<boolean> { return false; }

  // Contact and Embed Code methods
  async createContactInquiry?(inquiry: any): Promise<any> { return inquiry; }
  async getContactInquiries?(): Promise<any[]> { return []; }
  async updateContactInquiry?(id: number, update: any): Promise<any> { return update; }

  async getEmbedCodes(): Promise<EmbedCode[]> { return []; }
  async getEmbedCode(id: string): Promise<EmbedCode | undefined> { return undefined; }
  async createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode> { return embedCode; }
  async updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined> { return undefined; }
  async deleteEmbedCode(id: string): Promise<boolean> { return false; }
}
