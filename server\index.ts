import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, log } from "./vite";
import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

import { getAllowedEmailDomains, isEmailDomainRestrictionEnabled } from './general-config';
import { updateAllowedDomains } from '../shared/email-validator';
import cors from 'cors';
import { configStorage } from './config-storage';
import { initializeDefaultCheckoutPages } from './init-checkout-pages';
import { addTestOrders } from './add-test-orders';
import { updateExistingCheckoutPages } from './update-existing-checkout-pages';
import { privacyMiddleware, noIndexMiddleware, blockCrawlersMiddleware } from './middleware/privacy';
import { robotsRouter } from './routes/robots';

// Load environment variables from .env file
dotenv.config();

const app = express();

// Trust proxy when behind a reverse proxy (like <PERSON>in<PERSON> in CloudPanel)
app.set('trust proxy', 1);

app.use(cors({
  credentials: true,
  origin: true // Allow all origins in development, should be restricted in production
}));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Apply privacy and security middleware
app.use(privacyMiddleware);
app.use(blockCrawlersMiddleware);
app.use(noIndexMiddleware);

// Serve robots.txt
app.use('/robots.txt', robotsRouter);



// Debug middleware to log all incoming requests
app.use((req, res, next) => {
  console.log(`[DEBUG] ${req.method} ${req.path}`, req.body);
  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  console.log("Current environment:", app.get("env"));
  console.log("NODE_ENV:", process.env.NODE_ENV);

  // Use development mode when NODE_ENV is development
  const isDevMode = app.get("env") === "development";

  if (isDevMode) {
    console.log("Setting up Vite in development mode");
    await setupVite(app, server);
  } else {
    console.log("Setting up static file serving in production mode");

    // Serve static files from the dist/public directory
    const distPath = path.resolve(process.cwd(), "dist", "public");
    console.log(`Serving static files from: ${distPath}`);

    if (!fs.existsSync(distPath)) {
      console.error(`Error: Build directory not found: ${distPath}`);
      console.error("Make sure to run 'npm run build:prod' before starting the server in production mode");
      process.exit(1);
    }

    // Serve static files
    app.use(express.static(distPath));

    // Serve assets from the assets directory
    const assetsPath = path.join(distPath, 'assets');
    if (fs.existsSync(assetsPath)) {
      console.log(`Serving assets from: ${assetsPath}`);
      app.use('/assets', express.static(assetsPath));
    }

    // fall through to index.html if the file doesn't exist
    app.use("*", (req, res) => {
      // Skip API routes
      if (req.originalUrl.startsWith('/api')) {
        return res.status(404).json({ message: "API endpoint not found" });
      }

      console.log(`Serving index.html for: ${req.originalUrl}`);

      // For all other routes, serve the index.html file
      res.sendFile(path.resolve(distPath, "index.html"));
    });
  }

  // Serve the app on port 3001
  // this serves both the API and the client.
  const port = 3001;
  server.listen(port, () => {
    log(`serving on port ${port}`);

    // Initialize allowed email domains
    if (isEmailDomainRestrictionEnabled()) {
      const allowedDomains = getAllowedEmailDomains();
      updateAllowedDomains(allowedDomains);
      console.log('Initialized allowed email domains:', allowedDomains);
    }

    // Initialize default checkout pages
    initializeDefaultCheckoutPages();

    // Update existing checkout pages with confirmation messages
    updateExistingCheckoutPages();

    // Create test orders
    addTestOrders();

    // Initialize system monitoring
    (async () => {
      try {
        const { systemMonitor } = await import('./services/system-monitor');
        await systemMonitor.startMonitoring(5); // Start with 5-minute intervals
        console.log('🔍 System monitoring initialized');
      } catch (error) {
        console.error('Failed to initialize system monitoring:', error);
      }
    })();
  });
})();
