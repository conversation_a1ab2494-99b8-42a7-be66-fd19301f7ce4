import { db } from './db';
import { 
  products, 
  allowedEmails, 
  paypalButtons,
  emailTemplates,
  customCheckoutPages
} from '@shared/schema';
import { nanoid } from 'nanoid';

/**
 * Initialize the database with sample data
 */
export async function initializeDatabase() {
  try {
    console.log('🗄️ Initializing database with sample data...');

    // Check if products already exist
    const existingProducts = await db.select().from(products).limit(1);
    if (existingProducts.length > 0) {
      console.log('📦 Database already has data, skipping initialization');
      return;
    }

    // Insert sample products
    console.log('📦 Creating sample products...');
    const sampleProducts = [
      {
        name: "Dashboard Pro Template",
        description: "Modern admin dashboard template with 50+ components, dark/light mode, and responsive design. Perfect for productivity apps and SaaS platforms.",
        price: "89.99",
        imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      },
      {
        name: "Task Management UI Kit",
        description: "Complete UI kit for task management applications with 100+ screens, components, and interactive prototypes for Figma.",
        price: "59.99",
        imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      },
      {
        name: "Mobile Productivity App Template",
        description: "React Native template for productivity apps with calendar, notes, tasks, and team collaboration features. iOS & Android ready.",
        price: "129.99",
        imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      },
      {
        name: "Design System Starter Kit",
        description: "Complete design system with 200+ components, design tokens, documentation, and code examples for React and Vue.js.",
        price: "149.99",
        imageUrl: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      },
      {
        name: "Calendar & Scheduling Template",
        description: "Advanced calendar and scheduling template with booking system, time zones, recurring events, and team management features.",
        price: "79.99",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      },
      {
        name: "Note-Taking App UI Kit",
        description: "Beautiful note-taking app interface with rich text editor, markdown support, tags, and collaborative features. Includes Figma files.",
        price: "49.99",
        imageUrl: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "custom-link"
      }
    ];

    await db.insert(products).values(sampleProducts);
    console.log(`✅ Created ${sampleProducts.length} sample products`);

    // Insert sample allowed emails
    console.log('📧 Creating sample allowed emails...');
    const sampleEmails = [
      "<EMAIL>",
      "<EMAIL>", 
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ];

    const emailData = sampleEmails.map(email => ({
      email,
      notes: "Test email",
      lastSubject: "Welcome to our service",
      smtpProvider: "smtp-1",
      lastUpdated: new Date().toISOString(),
      createdAt: new Date().toISOString()
    }));

    await db.insert(allowedEmails).values(emailData);
    console.log(`✅ Created ${sampleEmails.length} sample allowed emails`);

    // Insert sample PayPal buttons
    console.log('💳 Creating sample PayPal buttons...');
    const sampleButtons = [
      {
        name: "Basic PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SAMPLE123456">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Standard PayPal Buy Now button",
        active: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Premium PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="PREMIUM789012">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Premium PayPal Pay Now button",
        active: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    await db.insert(paypalButtons).values(sampleButtons);
    console.log(`✅ Created ${sampleButtons.length} sample PayPal buttons`);

    console.log('🎉 Database initialization completed successfully!');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    throw error;
  }
}
