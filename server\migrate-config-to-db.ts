import Database from 'better-sqlite3';
import { configStorage } from './config-storage';

/**
 * Migration script to move SMTP providers and payment links from memory to SQLite database
 */
export async function migrateConfigToDatabase() {
  console.log('🔄 Starting migration of SMTP providers and payment links to database...');
  
  const sqlite = new Database('data.db');
  
  try {
    // Create SMTP providers table
    console.log('📋 Creating smtp_providers table...');
    sqlite.exec(`
      CREATE TABLE IF NOT EXISTS smtp_providers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        host TEXT NOT NULL,
        port TEXT NOT NULL,
        secure INTEGER NOT NULL DEFAULT 0,
        auth_user TEXT NOT NULL,
        auth_pass TEXT NOT NULL,
        from_email TEXT NOT NULL,
        from_name TEXT NOT NULL,
        active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        is_backup INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Create custom payment links table
    console.log('📋 Creating custom_payment_links table...');
    sqlite.exec(`
      CREATE TABLE IF NOT EXISTS custom_payment_links (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        payment_link TEXT NOT NULL,
        button_text TEXT NOT NULL,
        success_redirect_url TEXT,
        active INTEGER NOT NULL DEFAULT 1,
        is_trial_link INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);

    // Migrate SMTP providers from memory to database
    console.log('📧 Migrating SMTP providers...');
    const existingProviders = sqlite.prepare('SELECT COUNT(*) as count FROM smtp_providers').get() as { count: number };
    
    if (existingProviders.count === 0) {
      const emailConfig = configStorage.email;
      if (emailConfig && emailConfig.providers && emailConfig.providers.length > 0) {
        const insertProvider = sqlite.prepare(`
          INSERT INTO smtp_providers (
            id, name, host, port, secure, auth_user, auth_pass,
            from_email, from_name, active, is_default, is_backup,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        for (const provider of emailConfig.providers) {
          const now = new Date().toISOString();
          insertProvider.run(
            provider.id,
            provider.name,
            provider.credentials.host,
            provider.credentials.port,
            provider.credentials.secure ? 1 : 0,
            provider.credentials.auth.user,
            provider.credentials.auth.pass,
            provider.credentials.fromEmail,
            provider.credentials.fromName,
            provider.active ? 1 : 0,
            provider.isDefault ? 1 : 0,
            provider.isBackup ? 1 : 0,
            now,
            now
          );
          console.log(`✅ Migrated SMTP provider: ${provider.name}`);
        }
      }
    } else {
      console.log('📧 SMTP providers already exist in database, skipping migration');
    }

    // Migrate payment links from memory to database
    console.log('💳 Migrating payment links...');
    const existingLinks = sqlite.prepare('SELECT COUNT(*) as count FROM custom_payment_links').get() as { count: number };
    
    if (existingLinks.count === 0) {
      const paymentConfig = configStorage.payment;
      if (paymentConfig && paymentConfig.providers && paymentConfig.providers.length > 0) {
        const insertLink = sqlite.prepare(`
          INSERT INTO custom_payment_links (
            id, name, payment_link, button_text, success_redirect_url,
            active, is_trial_link, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        for (const provider of paymentConfig.providers) {
          if (provider.id === 'custom-link' || provider.id === 'trial-custom-link') {
            const config = provider.config as any;
            if (config.links && Array.isArray(config.links)) {
              const isTrialLink = provider.id === 'trial-custom-link';
              
              for (const link of config.links) {
                const now = new Date().toISOString();
                insertLink.run(
                  link.id,
                  link.name,
                  link.paymentLink,
                  link.buttonText,
                  link.successRedirectUrl || '',
                  link.active ? 1 : 0,
                  isTrialLink ? 1 : 0,
                  now,
                  now
                );
                console.log(`✅ Migrated payment link: ${link.name} (${isTrialLink ? 'trial' : 'regular'})`);
              }
            }
          }
        }
      }
    } else {
      console.log('💳 Payment links already exist in database, skipping migration');
    }

    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    sqlite.close();
  }
}

/**
 * Initialize default SMTP provider and payment links if none exist
 */
export async function initializeDefaultConfigs() {
  console.log('🔧 Initializing default configurations...');
  
  const sqlite = new Database('data.db');
  
  try {
    // Check if we have any SMTP providers
    const smtpCount = sqlite.prepare('SELECT COUNT(*) as count FROM smtp_providers').get() as { count: number };
    
    if (smtpCount.count === 0) {
      console.log('📧 Creating default SMTP provider...');
      const now = new Date().toISOString();
      
      sqlite.prepare(`
        INSERT INTO smtp_providers (
          id, name, host, port, secure, auth_user, auth_pass,
          from_email, from_name, active, is_default, is_backup,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'smtp-1',
        'Primary SMTP',
        'smtp-relay.brevo.com',
        '587',
        0,
        '<EMAIL>',
        '3d8I9xFm1yMDYj7W',
        '<EMAIL>',
        'Digital Invoice',
        1,
        1,
        0,
        now,
        now
      );
      
      console.log('✅ Default SMTP provider created');
    }

    // Check if we have any payment links
    const linksCount = sqlite.prepare('SELECT COUNT(*) as count FROM custom_payment_links').get() as { count: number };
    
    if (linksCount.count === 0) {
      console.log('💳 Creating default payment links...');
      const now = new Date().toISOString();
      
      // Create default regular payment link
      sqlite.prepare(`
        INSERT INTO custom_payment_links (
          id, name, payment_link, button_text, success_redirect_url,
          active, is_trial_link, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'link-1',
        'Default Payment Required',
        'https://example.com/pay',
        'Complete Payment',
        'https://example.com/thank-you',
        1,
        0,
        now,
        now
      );
      
      // Create default trial payment link
      sqlite.prepare(`
        INSERT INTO custom_payment_links (
          id, name, payment_link, button_text, success_redirect_url,
          active, is_trial_link, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'trial-link-1',
        'Default Trial Payment Link',
        'https://example.com/pay-trial',
        'Start Trial',
        'https://example.com/thank-you-trial',
        1,
        1,
        now,
        now
      );
      
      console.log('✅ Default payment links created');
    }
    
  } catch (error) {
    console.error('❌ Default config initialization failed:', error);
    throw error;
  } finally {
    sqlite.close();
  }
}
