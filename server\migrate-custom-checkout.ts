import Database from 'better-sqlite3';
import path from 'path';

const dbPath = path.join(process.cwd(), 'data.db');
const db = new Database(dbPath);

console.log('🔄 Starting custom checkout table migration...');

try {
  // Check if the columns already exist
  const tableInfo = db.prepare("PRAGMA table_info(custom_checkout_pages)").all();
  const existingColumns = tableInfo.map((col: any) => col.name);
  
  console.log('Existing columns:', existingColumns);
  
  // Add missing columns if they don't exist
  if (!existingColumns.includes('trial_custom_payment_link_id')) {
    console.log('Adding trial_custom_payment_link_id column...');
    db.exec('ALTER TABLE custom_checkout_pages ADD COLUMN trial_custom_payment_link_id TEXT');
  }
  
  if (!existingColumns.includes('trial_paypal_button_id')) {
    console.log('Adding trial_paypal_button_id column...');
    db.exec('ALTER TABLE custom_checkout_pages ADD COLUMN trial_paypal_button_id TEXT');
  }
  
  console.log('✅ Migration completed successfully!');
  
  // Verify the columns were added
  const updatedTableInfo = db.prepare("PRAGMA table_info(custom_checkout_pages)").all();
  console.log('Updated columns:', updatedTableInfo.map((col: any) => col.name));
  
} catch (error) {
  console.error('❌ Migration failed:', error);
} finally {
  db.close();
}
