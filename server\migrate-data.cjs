const Database = require('better-sqlite3');

const db = new Database('data.db');

console.log('🔄 Starting data migration...');

// Migrate general settings
const migrateGeneralSettings = () => {
  const generalConfig = {
    siteName: "🚀 TESTING - TemplateHub Pro",
    siteDescription: "🎯 TESTING - Premium productivity app templates and UI/UX design systems",
    logoUrl: "",
    faviconUrl: "",
    primaryColor: "#ff6b6b",
    secondaryColor: "#4ecdc4",
    footerText: "© 2024 🚀 TESTING - TemplateHub Pro",
    enableCheckout: true,
    enableCustomCheckout: true,
    enableTestMode: true,
    defaultTestCustomer: {
      enabled: true,
      name: "Test Designer",
      email: "<EMAIL>"
    },
    emailDomainRestriction: {
      enabled: false,
      allowedDomains: "gmail.com, hotmail.com, yahoo.com"
    },
    seoPrivacy: {
      globalNoIndex: true,
      hideFromSearchEngines: true,
      disableSitemaps: true,
      hideFramework: true,
      customRobotsTxt: "User-agent: *\nDisallow: /",
      pageIndexingRules: {
        homepage: false,
        checkoutPages: false,
        adminPages: false,
        customPages: false
      },
      privacyHeaders: {
        hideServerInfo: true,
        preventFraming: true,
        disableReferrer: true,
        hideGenerator: true
      }
    },
    telegramBot: {
      enabled: true,
      botToken: '8136736828:AAFz4hZMSslpUAZvJPO0MSzwiyYSIhQVeyk',
      adminChatId: '383368242',
      webhookUrl: 'https://00d6-196-74-45-154.ngrok-free.app/api/telegram/webhook',
      notifications: {
        newOrders: true,
        paymentConfirmations: true,
        trialUpgrades: true,
        orderStatusChanges: true
      },
      emailIntegration: {
        enabled: true,
        allowQuickSend: true,
        defaultTemplateId: 'smartonn_template'
      },
      m3uManagement: {
        enabled: true,
        autoExtractCredentials: true,
        credentialFormat: 'Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}',
        defaultM3uLinks: []
      },
      security: {
        verifyAdminOnly: true,
        rateLimitEnabled: true,
        auditLogging: true
      }
    }
  };

  const insertSql = `
    INSERT INTO general_settings (
      site_name, site_description, logo_url, favicon_url, primary_color, secondary_color,
      footer_text, enable_checkout, enable_custom_checkout, enable_test_mode,
      default_test_customer_enabled, default_test_customer_name, default_test_customer_email,
      email_domain_restriction_enabled, email_domain_restriction_domains,
      seo_privacy_settings, telegram_bot_settings, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const values = [
    generalConfig.siteName,
    generalConfig.siteDescription,
    generalConfig.logoUrl || null,
    generalConfig.faviconUrl || null,
    generalConfig.primaryColor,
    generalConfig.secondaryColor,
    generalConfig.footerText,
    generalConfig.enableCheckout ? 1 : 0,
    generalConfig.enableCustomCheckout ? 1 : 0,
    generalConfig.enableTestMode ? 1 : 0,
    generalConfig.defaultTestCustomer.enabled ? 1 : 0,
    generalConfig.defaultTestCustomer.name,
    generalConfig.defaultTestCustomer.email,
    generalConfig.emailDomainRestriction.enabled ? 1 : 0,
    generalConfig.emailDomainRestriction.allowedDomains,
    JSON.stringify(generalConfig.seoPrivacy),
    JSON.stringify(generalConfig.telegramBot),
    new Date().toISOString(),
    new Date().toISOString()
  ];

  db.prepare(insertSql).run(...values);
  console.log('✅ Migrated general settings data');
};

// Migrate homepage config
const migrateHomepageConfig = () => {
  const defaultHomepageConfig = {
    sections: [
      {
        id: 'hero-1',
        type: 'hero',
        title: 'Hero Section',
        enabled: true,
        order: 1,
        content: {
          title: 'Premium Productivity App Templates',
          subtitle: 'Design Systems & UI Kits',
          description: 'Build stunning productivity apps with our comprehensive collection of templates, components, and design systems. Professional, modern, and ready to use.',
          ctaText: 'Explore Templates',
          ctaLink: '#products',
          backgroundImage: '',
          backgroundType: 'gradient',
          backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          textColor: '#ffffff',
          showVideo: false,
          videoUrl: ''
        }
      }
    ],
    seo: {
      title: 'Productivity App Templates & UI/UX Design Systems',
      description: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
      keywords: 'productivity app templates, ui design system, app ui kit, dashboard templates, react components, design system, ui components',
      ogTitle: 'Productivity App Templates & UI/UX Design Systems',
      ogDescription: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
      ogImage: '',
      twitterTitle: 'Productivity App Templates & Design Systems',
      twitterDescription: 'Premium collection of productivity app templates and UI components for modern applications.',
      twitterImage: ''
    },
    theme: {
      primaryColor: '#6366f1',
      secondaryColor: '#4f46e5',
      accentColor: '#8b5cf6',
      backgroundColor: '#ffffff',
      textColor: '#1e293b',
      fontFamily: 'Inter, system-ui, sans-serif',
      borderRadius: '8px',
      spacing: '1rem'
    },
    version: 1
  };

  const insertSql = `
    INSERT INTO homepage_config (
      sections_data, seo_settings, theme_settings, version, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?)
  `;

  const values = [
    JSON.stringify(defaultHomepageConfig.sections),
    JSON.stringify(defaultHomepageConfig.seo),
    JSON.stringify(defaultHomepageConfig.theme),
    defaultHomepageConfig.version,
    new Date().toISOString(),
    new Date().toISOString()
  ];

  db.prepare(insertSql).run(...values);
  console.log('✅ Migrated homepage configuration data');
};

// Migrate system messages
const migrateSystemMessages = () => {
  const defaultMessages = [
    {
      id: "checkout_subscriber_only",
      category: "checkout",
      name: "Subscriber Only Message",
      description: "Message shown when checkout is restricted to subscribers only",
      content: "This checkout is currently available to subscribers only. Please contact support for access.",
      isHtml: false
    },
    {
      id: "checkout_maintenance",
      category: "checkout", 
      name: "Maintenance Mode Message",
      description: "Message shown when checkout is in maintenance mode",
      content: "Checkout is temporarily unavailable due to maintenance. Please try again later.",
      isHtml: false
    },
    {
      id: "payment_processing",
      category: "payment",
      name: "Payment Processing Message", 
      description: "Message shown during payment processing",
      content: "Your payment is being processed. Please do not close this window.",
      isHtml: false
    },
    {
      id: "order_confirmation",
      category: "order",
      name: "Order Confirmation Message",
      description: "Message shown after successful order completion",
      content: "Thank you for your order! You will receive a confirmation email shortly.",
      isHtml: false
    }
  ];

  const insertSql = `
    INSERT INTO system_messages (
      message_id, category, name, description, content, is_html, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const stmt = db.prepare(insertSql);

  for (const message of defaultMessages) {
    const values = [
      message.id,
      message.category,
      message.name,
      message.description || '',
      message.content,
      message.isHtml ? 1 : 0,
      new Date().toISOString(),
      new Date().toISOString()
    ];

    stmt.run(...values);
  }

  console.log(`✅ Migrated ${defaultMessages.length} system messages`);
};

// Run migrations
try {
  migrateGeneralSettings();
  migrateHomepageConfig();
  migrateSystemMessages();
  console.log('✅ All data migration completed successfully!');
} catch (error) {
  console.error('❌ Migration failed:', error);
} finally {
  db.close();
}
