import Database from 'better-sqlite3';
import { generalConfigStorage } from './general-config';
import { defaultHomepageConfig } from './homepage-config';
import { DEFAULT_SYSTEM_MESSAGES } from '../shared/system-messages';

const db = new Database('data.db');

// Create general_settings table
const createGeneralSettingsTable = () => {
  const sql = `
    CREATE TABLE IF NOT EXISTS general_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      site_name TEXT NOT NULL,
      site_description TEXT NOT NULL,
      logo_url TEXT,
      favicon_url TEXT,
      primary_color TEXT NOT NULL,
      secondary_color TEXT NOT NULL,
      footer_text TEXT NOT NULL,
      enable_checkout INTEGER NOT NULL DEFAULT 1,
      enable_custom_checkout INTEGER NOT NULL DEFAULT 1,
      enable_test_mode INTEGER NOT NULL DEFAULT 0,
      default_test_customer_enabled INTEGER NOT NULL DEFAULT 0,
      default_test_customer_name TEXT,
      default_test_customer_email TEXT,
      email_domain_restriction_enabled INTEGER NOT NULL DEFAULT 0,
      email_domain_restriction_domains TEXT,
      seo_privacy_settings TEXT,
      telegram_bot_settings TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `;
  
  db.exec(sql);
  console.log('✅ Created general_settings table');
};

// Create homepage_config table
const createHomepageConfigTable = () => {
  const sql = `
    CREATE TABLE IF NOT EXISTS homepage_config (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      sections_data TEXT NOT NULL,
      seo_settings TEXT NOT NULL,
      theme_settings TEXT NOT NULL,
      version INTEGER NOT NULL DEFAULT 1,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `;
  
  db.exec(sql);
  console.log('✅ Created homepage_config table');
};

// Create system_messages table
const createSystemMessagesTable = () => {
  const sql = `
    CREATE TABLE IF NOT EXISTS system_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      message_id TEXT NOT NULL UNIQUE,
      category TEXT NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      content TEXT NOT NULL,
      is_html INTEGER NOT NULL DEFAULT 0,
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `;
  
  db.exec(sql);
  console.log('✅ Created system_messages table');
};

// Migrate general settings data
const migrateGeneralSettings = () => {
  const checkSql = 'SELECT COUNT(*) as count FROM general_settings';
  const count = db.prepare(checkSql).get() as { count: number };
  
  if (count.count === 0) {
    const insertSql = `
      INSERT INTO general_settings (
        site_name, site_description, logo_url, favicon_url, primary_color, secondary_color,
        footer_text, enable_checkout, enable_custom_checkout, enable_test_mode,
        default_test_customer_enabled, default_test_customer_name, default_test_customer_email,
        email_domain_restriction_enabled, email_domain_restriction_domains,
        seo_privacy_settings, telegram_bot_settings, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      generalConfigStorage.siteName,
      generalConfigStorage.siteDescription,
      generalConfigStorage.logoUrl || null,
      generalConfigStorage.faviconUrl || null,
      generalConfigStorage.primaryColor,
      generalConfigStorage.secondaryColor,
      generalConfigStorage.footerText,
      generalConfigStorage.enableCheckout ? 1 : 0,
      generalConfigStorage.enableCustomCheckout ? 1 : 0,
      generalConfigStorage.enableTestMode ? 1 : 0,
      generalConfigStorage.defaultTestCustomer.enabled ? 1 : 0,
      generalConfigStorage.defaultTestCustomer.name,
      generalConfigStorage.defaultTestCustomer.email,
      generalConfigStorage.emailDomainRestriction.enabled ? 1 : 0,
      generalConfigStorage.emailDomainRestriction.allowedDomains,
      JSON.stringify(generalConfigStorage.seoPrivacy),
      JSON.stringify(generalConfigStorage.telegramBot),
      new Date().toISOString(),
      new Date().toISOString()
    ];
    
    db.prepare(insertSql).run(...values);
    console.log('✅ Migrated general settings data');
  } else {
    console.log('⏭️ General settings data already exists, skipping migration');
  }
};

// Migrate homepage configuration data
const migrateHomepageConfig = () => {
  const checkSql = 'SELECT COUNT(*) as count FROM homepage_config';
  const count = db.prepare(checkSql).get() as { count: number };
  
  if (count.count === 0) {
    const insertSql = `
      INSERT INTO homepage_config (
        sections_data, seo_settings, theme_settings, version, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      JSON.stringify(defaultHomepageConfig.sections),
      JSON.stringify(defaultHomepageConfig.seo),
      JSON.stringify(defaultHomepageConfig.theme),
      defaultHomepageConfig.version,
      new Date().toISOString(),
      new Date().toISOString()
    ];
    
    db.prepare(insertSql).run(...values);
    console.log('✅ Migrated homepage configuration data');
  } else {
    console.log('⏭️ Homepage configuration data already exists, skipping migration');
  }
};

// Migrate system messages data
const migrateSystemMessages = () => {
  const checkSql = 'SELECT COUNT(*) as count FROM system_messages';
  const count = db.prepare(checkSql).get() as { count: number };
  
  if (count.count === 0) {
    const insertSql = `
      INSERT INTO system_messages (
        message_id, category, name, description, content, is_html, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const stmt = db.prepare(insertSql);
    
    for (const message of DEFAULT_SYSTEM_MESSAGES) {
      const values = [
        message.id,
        message.category,
        message.name,
        message.description || '',
        message.content,
        message.isHtml ? 1 : 0,
        new Date().toISOString(),
        new Date().toISOString()
      ];
      
      stmt.run(...values);
    }
    
    console.log(`✅ Migrated ${DEFAULT_SYSTEM_MESSAGES.length} system messages`);
  } else {
    console.log('⏭️ System messages data already exists, skipping migration');
  }
};

// Main migration function
const runMigration = () => {
  console.log('🔄 Starting final database migration...');
  
  try {
    // Create tables
    createGeneralSettingsTable();
    createHomepageConfigTable();
    createSystemMessagesTable();
    
    // Migrate data
    migrateGeneralSettings();
    migrateHomepageConfig();
    migrateSystemMessages();
    
    console.log('✅ Final database migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    db.close();
  }
};

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration();
}

export { runMigration };
