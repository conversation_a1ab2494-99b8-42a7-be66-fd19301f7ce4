import express, { type Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import session from "express-session";
import createMemoryStore from "memorystore";
import { storage } from "./storage-factory";
import { checkoutSchema } from "@shared/schema";

import { sendInvoiceEmail, sendAdminOrderNotification, sendCustomerManualPaymentEmail } from "./services/email";
import { getPaymentConfig, configStorage } from "./config-storage";
import { ZodError } from "zod";
import adminRouter from "./routes/admin";
import authRouter from "./routes/auth";
import { customCheckoutRouter } from "./routes/custom-checkout";
import { allowedEmailsRouter } from "./routes/allowed-emails";
import { adminEmailRouter } from "./routes/admin-email";
import { sendEmailRouter } from "./routes/send-email";
import { emailTemplatesRouter } from "./routes/email-templates";

import { customInvoicesRouter } from "./routes/custom-invoices";
import { generalSettingsRouter } from "./routes/general-settings";
import { homepageRouter } from "./routes/homepage";
import devicesRouter from "./routes/devices";
import recoveryRouter from "./routes/recovery";

import uploadRouter from "./routes/upload";
import invoicesRouter from "./routes/invoices";
import { systemMessagesRouter } from "./routes/system-messages";
import { exportAllowedEmailsRouter } from "./routes/export-allowed-emails";
import redirectRouter from "./routes/redirect";
import { contactRouter } from "./routes/contact";
import { telegramRouter } from "./routes/telegram";
import { embedCodesRouter } from "./routes/embed-codes";
import systemMonitorRouter from "./routes/system-monitor";

// Initialize session store
const MemoryStore = createMemoryStore(session);

export async function registerRoutes(app: Express): Promise<Server> {
  // Setup session middleware
  app.use(session({
    store: new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    }),
    secret: process.env.SESSION_SECRET || 'paypal-invoice-secret',
    resave: false,
    saveUninitialized: false, // Don't create session until something stored
    cookie: {
      secure: process.env.NODE_ENV === 'production' && process.env.SECURE_COOKIES !== 'false', // Use secure cookies in production with HTTPS, unless explicitly disabled
      sameSite: 'lax',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    proxy: process.env.NODE_ENV === 'production' // Trust the reverse proxy when in production
  }));

  // Debug middleware to log session data
  app.use((req, res, next) => {
    if (req.path.startsWith('/api/admin')) {
      console.log(`Session debug [${req.path}]:`, {
        id: req.session.id,
        isAdmin: req.session.isAdmin,
        cookie: req.session.cookie
      });
    }
    next();
  });

  // API routes
  const apiRouter = express.Router();

  // Get all products
  apiRouter.get("/products", async (req: Request, res: Response) => {
    try {
      // Get all products
      let products = await storage.getProducts();

      // Only return active products
      products = products.filter(p => p.active);

      res.json(products);
    } catch (error) {
      console.error("Error fetching products:", error);
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  // Get product by ID
  apiRouter.get("/products/:id", async (req: Request, res: Response) => {
    try {
      const productId = parseInt(req.params.id);
      if (isNaN(productId)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }

      const product = await storage.getProduct(productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json(product);
    } catch (error) {
      console.error("Error fetching product:", error);
      res.status(500).json({ message: "Failed to fetch product" });
    }
  });

  // Create invoice
  apiRouter.post("/checkout", async (req: Request, res: Response) => {
    try {
      // Validate checkout data using Zod schema
      const checkoutData = checkoutSchema.parse(req.body);

      // Get product
      const product = await storage.getProduct(checkoutData.productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Get the active payment provider
      const paymentConfig = getPaymentConfig();
      const activeProvider = paymentConfig.providers.find(p => p.active);

      if (!activeProvider) {
        return res.status(500).json({
          message: 'No active payment provider configured'
        });
      }

      let invoiceResult;
      let paymentMethod = activeProvider.id;

      if (paymentMethod === 'custom-link') {
        // Generate custom payment link
        const { createCustomPaymentLink } = await import('./services/custom-link');
        invoiceResult = await createCustomPaymentLink(checkoutData, product);
      } else {
        return res.status(500).json({
          message: `Payment provider ${activeProvider.id} is not supported`
        });
      }
      const {
        id: paypalInvoiceId,
        url: paypalInvoiceUrl,
        isSimulated,
        error,
        isDraft,
        status,
        noPayPalAccount
      } = invoiceResult;

      console.log('PayPal invoice result:', {
        id: paypalInvoiceId,
        url: paypalInvoiceUrl,
        isSimulated: isSimulated || false,
        isDraft: isDraft || false,
        status: status || 'N/A',
        noPayPalAccount: noPayPalAccount || false
      });

      // Determine the invoice status
      let invoiceStatus = "sent";
      let invoiceNotes;

      if (noPayPalAccount) {
        // If the customer's email is not valid, mark the invoice as "no_paypal"
        invoiceStatus = "no_paypal";
        invoiceNotes = `No PayPal invoice generated because the customer email is not valid.`;
      } else if (isSimulated) {
        invoiceStatus = "simulated";
        invoiceNotes = `Simulated invoice due to API error: ${error}`;
      } else if (isDraft) {
        invoiceStatus = "draft";
        invoiceNotes = `Invoice created in draft status. Status: ${status}`;
      }

      // Create invoice in our storage
      const invoice = await storage.createInvoice({
        customerName: checkoutData.fullName,
        customerEmail: checkoutData.email,
        productId: product.id,
        amount: product.price,
        status: invoiceStatus,
        paypalInvoiceId,
        paypalInvoiceUrl,
        createdAt: new Date().toISOString(),
        notes: invoiceNotes
      });

      // Send admin notification email for manual payment processing
      try {
        await sendAdminOrderNotification(checkoutData, product, invoice);
        console.log(`Admin notification email sent for invoice ID: ${invoice.id}`);
      } catch (emailError) {
        console.warn("Admin notification email could not be sent:", emailError);
        // We don't want to fail the checkout if just the email fails
      }

      // Send customer confirmation email for manual payment
      try {
        await sendCustomerManualPaymentEmail(checkoutData, product);
        console.log(`Customer manual payment email sent for invoice ID: ${invoice.id}`);
      } catch (emailError) {
        console.warn("Customer email notification could not be sent:", emailError);
        // We don't want to fail the checkout if just the email fails
      }

      // Send Telegram notification
      try {
        const { telegramBot } = await import('./services/telegram-bot');
        await telegramBot.sendOrderNotification({
          orderId: invoice.id,
          customerName: checkoutData.fullName,
          customerEmail: checkoutData.email,
          amount: product.price,
          status: invoiceStatus,
          country: checkoutData.country || 'Unknown',
          appType: checkoutData.appType || 'Unknown',
          isTrialOrder: false,
          createdAt: invoice.createdAt,
          macAddress: checkoutData.macAddress
        });
        console.log(`Telegram notification sent for invoice ID: ${invoice.id}`);
      } catch (telegramError) {
        console.warn("Telegram notification could not be sent:", telegramError);
        // We don't want to fail the checkout if just the Telegram notification fails
      }

      // Return success response
      res.status(201).json({
        invoiceId: invoice.id,
        paypalInvoiceId,
        paypalInvoiceUrl,
        isSimulated: isSimulated || false,
        isDraft: isDraft || false,
        noPayPalAccount: noPayPalAccount || false,
        status: status || 'SENT',
        message: `Order received successfully! We will send you payment details manually via email within 24 hours. Please check your inbox for further instructions.`
      });

    } catch (error) {
      console.error("Error processing checkout:", error);

      if (error instanceof ZodError) {
        return res.status(400).json({
          message: "Validation error",
          errors: error.errors
        });
      }

      res.status(500).json({
        message: "Failed to process checkout",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Register API routes with /api prefix
  app.use("/api", apiRouter);

  // Register admin routes
  app.use("/api/admin", adminRouter);

  // Register auth routes
  app.use("/api/admin", authRouter);

  // Register custom checkout routes
  app.use("/api/custom-checkout", customCheckoutRouter);

  // Register allowed emails routes
  app.use("/api/allowed-emails", allowedEmailsRouter);

  // Register admin email routes
  app.use("/api/admin/email", adminEmailRouter);

  // Register send email routes
  app.use("/api/send-email", sendEmailRouter);

  // Register email templates routes
  app.use("/api/email-templates", emailTemplatesRouter);



  // Register custom invoices routes
  app.use("/api/custom-invoices", customInvoicesRouter);

  // Register general settings routes
  app.use("/api/general-settings", generalSettingsRouter);

  // Register homepage routes
  app.use("/api/homepage", homepageRouter);

  // Register devices routes
  app.use(devicesRouter);

  // Register recovery routes
  app.use(recoveryRouter);



  // Register upload routes
  app.use("/api/upload", uploadRouter);

  // Register invoices routes
  app.use("/api/invoices", invoicesRouter);

  // Register system messages routes
  app.use("/api/system-messages", systemMessagesRouter);

  // Register export allowed emails route
  app.use("/api/export-allowed-emails", exportAllowedEmailsRouter);

  // Register redirect routes
  app.use("/api/redirect", redirectRouter);

  // Register short redirect routes (e.g., /r/keyword)
  app.use("/r", redirectRouter);

  // Register contact routes
  app.use("/api/contact", contactRouter);

  // Register Telegram bot routes
  app.use("/api/telegram", telegramRouter);

  // Register embed codes routes
  app.use("/api/embed-codes", embedCodesRouter);

  // Register system monitor routes
  app.use("/api/admin/system-monitor", systemMonitorRouter);

  // Serve uploaded files
  app.use("/uploads", express.static(process.cwd() + "/uploads"));

  // Direct route for adding trial payment links
  app.post("/api/trial-payment-link/add", (req: Request, res: Response) => {
    console.log('Direct route for adding trial payment link called with body:', req.body);

    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    // Find the Trial Custom Link provider
    const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
    console.log('Trial custom link provider index:', providerIndex);

    if (providerIndex === -1) {
      console.error('Trial custom payment link provider not found');
      return res.status(404).json({
        message: 'Trial custom payment link provider not found'
      });
    }

    // Get the current links
    const config = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((config as any).links) ? (config as any).links : [];

    // Generate a unique ID for the new link
    const linkId = `trial-link-${Date.now()}`;

    // Add the new link
    links.push({
      id: linkId,
      name: name || `Trial Payment Link ${links.length + 1}`,
      paymentLink: paymentLink || '',
      buttonText: buttonText || 'Start Trial',
      successRedirectUrl: successRedirectUrl || '',
      active: active !== undefined ? active : true
    });

    // Update the config
    configStorage.payment.providers[providerIndex].config = {
      ...(config as any),
      links
    };

    console.log('Added new trial custom payment link (direct route):',
      JSON.stringify(links[links.length - 1], null, 2));

    res.json({
      message: 'Trial custom payment link added successfully',
      link: links[links.length - 1]
    });
  });

  // Add the missing /api/direct-trial-link route that frontend expects
  app.post("/api/direct-trial-link", (req: Request, res: Response) => {
    console.log('Direct trial link route called with body:', req.body);

    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    // Find the Trial Custom Link provider
    const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
    console.log('Trial custom link provider index:', providerIndex);

    if (providerIndex === -1) {
      console.error('Trial custom payment link provider not found');
      return res.status(404).json({
        message: 'Trial custom payment link provider not found'
      });
    }

    // Get the current links
    const config = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((config as any).links) ? (config as any).links : [];

    // Generate a unique ID for the new link
    const linkId = `trial-link-${Date.now()}`;

    // Add the new link
    links.push({
      id: linkId,
      name: name || `Trial Payment Link ${links.length + 1}`,
      paymentLink: paymentLink || '',
      buttonText: buttonText || 'Start Trial',
      successRedirectUrl: successRedirectUrl || '',
      active: active !== undefined ? active : true
    });

    // Update the config
    configStorage.payment.providers[providerIndex].config = {
      ...(config as any),
      links
    };

    console.log('Added new trial custom payment link (direct-trial-link route):',
      JSON.stringify(links[links.length - 1], null, 2));

    res.json({
      message: 'Trial custom payment link added successfully',
      link: links[links.length - 1]
    });
  });

  const httpServer = createServer(app);
  return httpServer;
}
