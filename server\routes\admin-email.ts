import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';
import { sendCustomEmail } from '../services/email';

export const adminEmailRouter = Router();

// Test endpoint to verify the router is working
adminEmailRouter.get('/test', (req: Request, res: Response) => {
  res.json({ message: 'Admin email router is working!' });
});

// Test endpoint for email templates
adminEmailRouter.get('/email-templates/test', (req: Request, res: Response) => {
  res.json({ message: 'Email templates endpoint is working!' });
});

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Schema for creating/updating email templates
const emailTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required')
});

// Schema for sending emails
const sendEmailSchema = z.object({
  orderId: z.number().optional(),
  to: z.string().email('Valid email is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required')
});

// Get all email templates
adminEmailRouter.get('/email-templates', checkAdmin, async (req: Request, res: Response) => {
  try {
    const templates = await storage.getEmailTemplates();
    res.json(templates);
  } catch (error) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({ message: 'Failed to fetch email templates' });
  }
});

// Get email template by ID
adminEmailRouter.get('/email-templates/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }

    const template = await storage.getEmailTemplate(id);

    if (!template) {
      return res.status(404).json({ message: 'Email template not found' });
    }

    res.json(template);
  } catch (error) {
    console.error('Error fetching email template:', error);
    res.status(500).json({ message: 'Failed to fetch email template' });
  }
});

// Create a new email template
adminEmailRouter.post('/email-templates', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = emailTemplateSchema.parse(req.body);

    // Generate a unique template ID
    const templateId = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const template = await storage.createEmailTemplate({
      templateId,
      name: validatedData.name,
      description: `Template created from send email - ${new Date().toLocaleDateString()}`,
      subject: validatedData.subject,
      htmlContent: validatedData.content,
      textContent: validatedData.content, // Use same content for text version
      content: validatedData.content, // For backward compatibility
      category: 'general',
      isDefault: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    res.status(201).json(template);
  } catch (error) {
    console.error('Error creating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create email template' });
  }
});

// Update an email template
adminEmailRouter.put('/email-templates/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }

    const validatedData = emailTemplateSchema.parse(req.body);

    const template = await storage.getEmailTemplate(id);

    if (!template) {
      return res.status(404).json({ message: 'Email template not found' });
    }

    const updatedTemplate = await storage.updateEmailTemplate(id, {
      name: validatedData.name,
      subject: validatedData.subject,
      htmlContent: validatedData.content,
      textContent: validatedData.content, // Use same content for text version
      content: validatedData.content, // For backward compatibility
      updatedAt: new Date().toISOString()
    });

    res.json(updatedTemplate);
  } catch (error) {
    console.error('Error updating email template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update email template' });
  }
});

// Delete an email template
adminEmailRouter.delete('/email-templates/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }

    const template = await storage.getEmailTemplate(id);

    if (!template) {
      return res.status(404).json({ message: 'Email template not found' });
    }

    const deleted = await storage.deleteEmailTemplate(id);

    if (deleted) {
      res.json({ message: 'Email template deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete email template' });
    }
  } catch (error) {
    console.error('Error deleting email template:', error);
    res.status(500).json({ message: 'Failed to delete email template' });
  }
});

// Send an email
adminEmailRouter.post('/send-email', checkAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Received request to send email:', req.body);
    const validatedData = sendEmailSchema.parse(req.body);

    // No longer extracting username from email content

    // For now, we'll just simulate sending the email
    // In a real implementation, you would use sendCustomEmail
    console.log('Sending email to:', validatedData.to);
    console.log('Subject:', validatedData.subject);
    console.log('Content:', validatedData.content);

    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Update the order with the email sent information if orderId is provided
    if (validatedData.orderId) {
      const order = await storage.getInvoice(validatedData.orderId);

      if (order) {
        const notes = order.notes
          ? `${order.notes}\n\nEmail sent on ${new Date().toISOString()} - Subject: ${validatedData.subject}`
          : `Email sent on ${new Date().toISOString()} - Subject: ${validatedData.subject}`;

        await storage.updateInvoice(validatedData.orderId, { notes });
      }
    }

    res.json({
      message: 'Email sent successfully'
    });
  } catch (error) {
    console.error('Error sending email:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
