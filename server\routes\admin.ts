import { Router, Request, Response, NextFunction } from 'express';
import { storage } from '../storage-factory';
import { insertProductSchema } from '@shared/schema';
import { ZodError } from 'zod';
import { randomBytes, createHash } from 'crypto';
import { configStorage } from '../config-storage';
import { telegramBot } from '../services/telegram-bot';

const adminRouter = Router();

// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
    pendingApprovalId?: string;
    pending2FARequestId?: string;
  }
}

// Admin access token middleware removed - no longer needed

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session:', req.session);

  // Check if user is authenticated and has completed 2FA if required
  if (req.session.isAdmin) {
    // If 2FA is required but not verified, deny access
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified:', true);
    next();
  } else {
    console.log('Admin session verified:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Check if user is authenticated
adminRouter.get('/check-session', (req: Request, res: Response) => {
  console.log('Checking session:', {
    id: req.session.id,
    isAdmin: req.session.isAdmin,
    username: req.session.username,
    requiresTwoFactor: req.session.requiresTwoFactor,
    twoFactorVerified: req.session.twoFactorVerified,
    cookie: req.session.cookie
  });

  if (req.session.isAdmin) {
    // User is fully authenticated
    res.status(200).json({
      isAuthenticated: true,
      user: {
        username: req.session.username
      }
    });
  } else if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
    // User has completed first factor but needs to complete 2FA
    res.status(200).json({
      isAuthenticated: false,
      requiresTwoFactor: true,
      message: 'Two-factor authentication required'
    });
  } else {
    // User is not authenticated at all
    res.status(200).json({
      isAuthenticated: false,
      message: 'Not authenticated'
    });
  }
});

// Admin login
adminRouter.post('/login', async (req: Request, res: Response) => {
  const { username, password, rememberMe } = req.body;

  // Get client IP and user agent for security tracking
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  const sessionId = req.session.id || 'unknown';

  console.log('Login attempt for:', username, 'from IP:', clientIP);

  try {
    // Get user from storage (single call)
    let authenticatedUser = await storage.getUserByUsername(username);

    // If user not found, create default admin user for demo purposes
    if (!authenticatedUser && username === 'admin' && password === 'admin123') {
      // Create default admin user
      authenticatedUser = await storage.createUser({
        username: 'admin',
        password: 'admin123', // This will be hashed in the storage layer
        email: '<EMAIL>',
        isAdmin: true,
        rememberMe: false
      });
    }

    // Verify credentials (optimized - use user object to avoid double lookup)
    const isValidCredentials = authenticatedUser ?
      await storage.verifyUserCredentialsWithUser(authenticatedUser, password) : false;

    if (isValidCredentials) {
      // Get security settings
      const securitySettings = await configStorage.getSecuritySettings();

      // Send login alert if enabled
      if (securitySettings.loginAlertsEnabled) {
        await telegramBot.sendLoginAlert(username, clientIP, userAgent, true, sessionId);
      }

      // Check IP restriction if enabled
      if (securitySettings.ipRestrictionEnabled) {
        const isIPApproved = await telegramBot.isIPApproved(clientIP);
        if (!isIPApproved) {
          console.log('New IP detected, requesting approval:', clientIP);

          // Request approval for new IP
          const approvalId = await telegramBot.requestLoginApproval(username, clientIP, userAgent, sessionId);

          // Store approval ID in session for checking later
          req.session.pendingApprovalId = approvalId;

          return res.status(202).json({
            message: 'New IP address detected. Approval required.',
            requiresApproval: true,
            approvalId: approvalId
          });
        }
      }

      // Check if 2FA is enabled for this user AND Telegram 2FA is enabled in settings
      if (authenticatedUser && authenticatedUser.twoFactorEnabled && securitySettings.telegram2FAEnabled) {
        // Set up session for 2FA verification
        req.session.requiresTwoFactor = true;
        req.session.userId = authenticatedUser.id;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.twoFactorVerified = false;
        req.session.isAdmin = false; // Will be set to true after 2FA verification

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        // Request 2FA approval via Telegram
        const twoFactorRequestId = await telegramBot.request2FAApproval(username, sessionId);
        req.session.pending2FARequestId = twoFactorRequestId;

        console.log('First-factor authentication successful, 2FA required:', {
          id: req.session.id,
          username: req.session.username,
          requiresTwoFactor: req.session.requiresTwoFactor,
          twoFactorRequestId: twoFactorRequestId
        });

        // Return response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Two-factor authentication required',
          requiresTwoFactor: true,
          isAuthenticated: false,
          twoFactorRequestId: twoFactorRequestId
        });
      } else {
        // No 2FA required, complete login
        req.session.isAdmin = true;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.requiresTwoFactor = false;
        req.session.twoFactorVerified = false;

        if (authenticatedUser) {
          req.session.userId = authenticatedUser.id;
        }

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        console.log('Login successful, session info:', {
          id: req.session.id,
          isAdmin: req.session.isAdmin,
          username: req.session.username,
          rememberMe: req.session.rememberMe,
          cookieMaxAge: req.session.cookie.maxAge
        });

        // Set a non-httpOnly cookie as a flag for the frontend
        res.cookie('isLoggedIn', 'true', {
          httpOnly: false,
          maxAge: req.session.cookie.maxAge,
          path: '/',
          sameSite: 'lax'
        });

        // Return success response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Login successful',
          user: { username },
          isAuthenticated: true,
          rememberMe: req.session.rememberMe,
          requiresTwoFactor: false
        });
      }
    } else {
      // Send login alert for failed login attempt if enabled
      const securitySettings = await configStorage.getSecuritySettings();
      if (securitySettings.loginAlertsEnabled) {
        await telegramBot.sendLoginAlert(username, clientIP, userAgent, false);
      }
      res.status(401).json({ message: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Login failed - server error' });
  }
});

// Check login approval status
adminRouter.get('/check-approval/:approvalId', async (req: Request, res: Response) => {
  try {
    const { approvalId } = req.params;
    const status = telegramBot.getLoginApprovalStatus(approvalId);

    res.json({ status });
  } catch (error) {
    console.error('Error checking approval status:', error);
    res.status(500).json({ message: 'Failed to check approval status' });
  }
});

// Check 2FA status
adminRouter.get('/check-2fa/:requestId', async (req: Request, res: Response) => {
  try {
    const { requestId } = req.params;
    const status = telegramBot.get2FAStatus(requestId);

    if (status === 'approved') {
      // Complete the login process
      req.session.isAdmin = true;
      req.session.twoFactorVerified = true;
      req.session.requiresTwoFactor = false;

      // Set a non-httpOnly cookie as a flag for the frontend
      res.cookie('isLoggedIn', 'true', {
        httpOnly: false,
        maxAge: req.session.cookie.maxAge,
        path: '/',
        sameSite: 'lax'
      });
    }

    res.json({ status });
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    res.status(500).json({ message: 'Failed to check 2FA status' });
  }
});

// Admin logout
adminRouter.post('/logout', (req: Request, res: Response) => {
  console.log('Logout requested, destroying session:', req.session.id);

  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
      return res.status(500).json({ message: 'Logout failed' });
    }

    // Clear the cookie
    res.clearCookie('isLoggedIn');
    res.clearCookie('connect.sid', { path: '/' });

    res.status(200).json({ message: 'Logout successful' });
  });
});

// Enable/Disable 2FA for current user
adminRouter.post('/toggle-2fa', isAdmin, async (req: Request, res: Response) => {
  try {
    const { enabled } = req.body;
    const userId = req.session.userId;

    if (!userId) {
      return res.status(400).json({ message: 'User ID not found in session' });
    }

    // Update user's 2FA setting
    await storage.updateUser(userId, { twoFactorEnabled: enabled });

    res.json({
      message: `Two-factor authentication ${enabled ? 'enabled' : 'disabled'} successfully`,
      twoFactorEnabled: enabled
    });
  } catch (error) {
    console.error('Error toggling 2FA:', error);
    res.status(500).json({ message: 'Failed to update 2FA settings' });
  }
});

// Get security settings
adminRouter.get('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const securitySettings = await configStorage.getSecuritySettings();

    // Also get user's 2FA status
    const userId = req.session.userId;
    let userTwoFactorEnabled = false;

    if (userId) {
      const user = await storage.getUser(userId);
      if (user) {
        userTwoFactorEnabled = user.twoFactorEnabled || false;
      }
    }

    res.json({
      ...securitySettings,
      userTwoFactorEnabled,
      username: req.session.username
    });
  } catch (error) {
    console.error('Error fetching security settings:', error);
    res.status(500).json({ message: 'Failed to fetch security settings' });
  }
});

// Update security settings
adminRouter.post('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const { loginAlertsEnabled, ipRestrictionEnabled, telegram2FAEnabled } = req.body;

    const updates = {
      loginAlertsEnabled: Boolean(loginAlertsEnabled),
      ipRestrictionEnabled: Boolean(ipRestrictionEnabled),
      telegram2FAEnabled: Boolean(telegram2FAEnabled)
    };

    const updatedSettings = await configStorage.updateSecuritySettings(updates);

    res.json({
      message: 'Security settings updated successfully',
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Error updating security settings:', error);
    res.status(500).json({ message: 'Failed to update security settings' });
  }
});

// Get admin dashboard stats
adminRouter.get('/stats', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    const invoices = await storage.getInvoices();

    const activeProducts = products.filter(p => p.active).length;
    const totalSales = invoices.length;
    const pendingInvoices = invoices.filter(i => i.status === 'pending').length;
    const paidInvoices = invoices.filter(i => i.status === 'paid').length;

    // Calculate total revenue from paid invoices
    const revenue = invoices
      .filter(i => i.status === 'paid')
      .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0);

    res.json({
      products: {
        total: products.length,
        active: activeProducts
      },
      sales: {
        total: totalSales,
        pending: pendingInvoices,
        completed: paidInvoices,
        revenue: revenue.toFixed(2)
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ message: 'Failed to fetch dashboard stats' });
  }
});

// Get all invoices
adminRouter.get('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoices = await storage.getInvoices();
    const customCheckoutPages = await storage.getCustomCheckoutPages();

    console.log('Raw invoices:', JSON.stringify(invoices, null, 2));
    console.log('Custom checkout pages:', JSON.stringify(customCheckoutPages, null, 2));

    // Enhance invoices with product name and checkout page info for display
    const enhancedInvoices = await Promise.all(invoices.map(async (invoice) => {
      const product = await storage.getProduct(invoice.productId);

      // Check if this invoice was created from a custom checkout page
      let checkoutPageInfo = null;
      if (invoice.customCheckoutPageId) {
        checkoutPageInfo = customCheckoutPages.find(page => page.id === invoice.customCheckoutPageId);
        console.log(`Found checkout page for invoice ${invoice.id}:`, checkoutPageInfo);
      } else {
        console.log(`No customCheckoutPageId for invoice ${invoice.id}`);

        // Try to find a matching checkout page based on the product ID
        // This is a fallback for older orders that don't have customCheckoutPageId
        const matchingPage = customCheckoutPages.find(page => {
          // For trial orders, find a trial checkout page
          if (invoice.isTrialOrder) {
            return page.isTrialCheckout;
          }
          // For regular orders, find a regular checkout page
          return !page.isTrialCheckout;
        });

        if (matchingPage) {
          console.log(`Found matching checkout page for invoice ${invoice.id} based on type:`, matchingPage);
          checkoutPageInfo = matchingPage;

          // Update the invoice with the checkout page ID for future reference
          await storage.updateInvoice(invoice.id, { customCheckoutPageId: matchingPage.id });
        }
      }

      return {
        ...invoice,
        productName: product ? product.name : 'Unknown Product',
        checkoutPageTitle: checkoutPageInfo ? checkoutPageInfo.title : null,
        checkoutPageId: checkoutPageInfo ? checkoutPageInfo.id : null,
        smtpProviderId: checkoutPageInfo ? checkoutPageInfo.smtpProviderId : null
      };
    }));

    res.json(enhancedInvoices);
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ message: 'Failed to fetch invoices' });
  }
});

// Create new invoice (for testing purposes)
adminRouter.post('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount, status, createdAt } = req.body;

    // Validate required fields
    if (!customerName || !customerEmail || !productId || !amount) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Create the invoice
    const invoice = await storage.createInvoice({
      customerName,
      customerEmail,
      productId: Number(productId),
      amount: amount.toString(),
      status: status || 'pending',
      createdAt: createdAt || new Date().toISOString()
    });

    console.log('Created test invoice:', invoice);

    res.status(201).json(invoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({
      message: 'Failed to create invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update invoice
adminRouter.put('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { customerName, customerEmail, status, notes, checkForTrialUpgrade, smtpProviderId } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Only update fields that are actually provided (not undefined)
    const updateData: any = {};
    if (customerName !== undefined) updateData.customerName = customerName;
    if (customerEmail !== undefined) updateData.customerEmail = customerEmail;
    if (status !== undefined) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;
    if (smtpProviderId !== undefined) updateData.smtpProviderId = smtpProviderId;

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, updateData);

    console.log('Updated invoice:', updatedInvoice);

    // Check if we need to upgrade a trial order
    let trialOrderUpgraded = false;

    if (checkForTrialUpgrade && status && status.toLowerCase() === 'paid' && customerEmail) {
      console.log(`Checking for trial orders to upgrade for customer email: ${customerEmail}`);

      // Find trial orders with the same email that haven't been upgraded yet
      const allInvoices = await storage.getInvoices();
      const trialOrders = allInvoices.filter(invoice =>
        invoice.customerEmail === customerEmail &&
        invoice.isTrialOrder === true &&
        invoice.hasUpgraded !== true &&
        invoice.id !== invoiceId // Don't include the current invoice
      );

      console.log(`Found ${trialOrders.length} trial orders for customer ${customerEmail} that can be upgraded`);

      if (trialOrders.length > 0) {
        // Upgrade the trial order(s)
        for (const trialOrder of trialOrders) {
          await storage.updateInvoice(trialOrder.id, {
            hasUpgraded: true,
            upgradedAt: new Date().toISOString()
          });
          console.log(`Automatically upgraded trial order ${trialOrder.id} for customer ${customerEmail}`);
          trialOrderUpgraded = true;
        }
      }
    }

    res.json({
      message: 'Invoice updated successfully',
      invoice: updatedInvoice,
      trialOrderUpgraded
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({
      message: 'Failed to update invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Mark invoice as upgraded
adminRouter.put('/invoices/:id/upgrade', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { hasUpgraded, upgradedAt } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, {
      hasUpgraded,
      upgradedAt
    });

    console.log('Marked invoice as upgraded:', updatedInvoice);

    res.json({
      message: 'Invoice marked as upgraded successfully',
      invoice: updatedInvoice
    });
  } catch (error) {
    console.error('Error marking invoice as upgraded:', error);
    res.status(500).json({
      message: 'Failed to mark invoice as upgraded',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete invoice (remove customer)
adminRouter.delete('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Delete the invoice from storage
    const deleted = await storage.deleteInvoice(invoiceId);
    if (!deleted) {
      throw new Error('Failed to delete invoice from database');
    }
    console.log(`Deleted invoice with ID ${invoiceId}`);

    res.json({
      message: 'Customer removed successfully',
      invoiceId
    });
  } catch (error) {
    console.error('Error removing customer:', error);
    res.status(500).json({
      message: 'Failed to remove customer',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all products for admin
adminRouter.get('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ message: 'Failed to fetch products' });
  }
});

// Create new product
adminRouter.post('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const productData = insertProductSchema.parse(req.body);
    const product = await storage.createProduct(productData);
    res.status(201).json(product);
  } catch (error) {
    console.error('Error creating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create product' });
  }
});

// Update product
adminRouter.patch('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // In a real application with a database, this would use a real update operation
    // For our in-memory store, we'll get the product, validate the update, and simulate the update
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Since we don't have a real update method in storage, let's simulate it
    // In a real app with a DB, you'd use storage.updateProduct() instead
    const updatedProduct = {
      ...existingProduct,
      ...req.body,
      id: productId  // Ensure ID doesn't change
    };

    // Validate the combined object
    insertProductSchema.parse(updatedProduct);

    // Simulate updating the product (would be a DB update in a real app)
    // This is a hack for our demo app; in a real app with a DB, you'd use storage.updateProduct
    const allProducts = await storage.getProducts();
    const productIndex = allProducts.findIndex(p => p.id === productId);
    if (productIndex !== -1) {
      allProducts[productIndex] = updatedProduct;
    }

    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update product' });
  }
});

// Delete product
adminRouter.delete('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // Check if product exists
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // In a real app with a DB, you'd use a storage.deleteProduct method
    // Since we're using in-memory storage for this demo, we'll simulate deletion
    const allProducts = await storage.getProducts();
    const updatedProducts = allProducts.filter(p => p.id !== productId);

    // This is a hack for our demo app; in a real app with DB, use storage.deleteProduct
    (storage as any).products = new Map(updatedProducts.map(p => [p.id, p]));

    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ message: 'Failed to delete product' });
  }
});

// Email configuration endpoints - MOVED TO admin-config.ts for database storage
// adminRouter.get('/email-config', isAdmin, (req: Request, res: Response) => {
//   res.json(configStorage.email);
// });

adminRouter.post('/email-config', isAdmin, (req: Request, res: Response) => {
  const { providerId, name, config, active, isDefault, isBackup } = req.body;

  // If this provider is being set as default, unset default flag on all other providers
  if (isDefault) {
    configStorage.email.providers.forEach(provider => {
      if (provider.id !== providerId) {
        provider.isDefault = false;
      }
    });
  }

  // Find the provider to update
  const providerIndex = configStorage.email.providers.findIndex(p => p.id === providerId);

  if (providerIndex !== -1) {
    // Update existing provider
    configStorage.email.providers[providerIndex] = {
      ...configStorage.email.providers[providerIndex],
      name: name || configStorage.email.providers[providerIndex].name,
      active,
      isDefault,
      isBackup,
      credentials: {
        host: config.host || '',
        port: config.port || '587',
        secure: config.secure || false,
        auth: {
          user: config.username || '',
          pass: config.password || ''
        },
        fromEmail: config.fromEmail || '',
        fromName: config.fromName || ''
      }
    };

    console.log(`Updated email config for ${providerId}:`,
      JSON.stringify(configStorage.email.providers[providerIndex], null, 2));
  } else {
    // Create new provider
    const newProvider = {
      id: providerId,
      name: name || 'SMTP Configuration',
      active,
      isDefault,
      isBackup,
      credentials: {
        host: config.host || '',
        port: config.port || '587',
        secure: config.secure || false,
        auth: {
          user: config.username || '',
          pass: config.password || ''
        },
        fromEmail: config.fromEmail || '',
        fromName: config.fromName || ''
      }
    };

    configStorage.email.providers.push(newProvider);
    console.log(`Created new email config with ID ${providerId}:`,
      JSON.stringify(newProvider, null, 2));
  }

  res.json({
    message: 'Email configuration updated successfully',
    config: configStorage.email
  });
});

adminRouter.post('/email-config/delete', isAdmin, (req: Request, res: Response) => {
  const { providerId } = req.body;

  // Find the provider to delete
  const providerIndex = configStorage.email.providers.findIndex(p => p.id === providerId);

  if (providerIndex !== -1) {
    // Check if this is the default provider
    if (configStorage.email.providers[providerIndex].isDefault) {
      return res.status(400).json({
        message: 'Cannot delete the default SMTP configuration. Please set another configuration as default first.'
      });
    }

    // Remove the provider
    configStorage.email.providers.splice(providerIndex, 1);
    console.log(`Deleted email config with ID ${providerId}`);

    res.json({
      message: 'Email configuration deleted successfully',
      config: configStorage.email
    });
  } else {
    res.status(404).json({ message: 'Email configuration not found' });
  }
});

adminRouter.post('/email-test', isAdmin, async (req: Request, res: Response) => {
  const { email, providerId } = req.body;

  if (!email) {
    return res.status(400).json({ message: 'Email address is required' });
  }

  try {
    // Import dynamically to avoid circular dependencies
    const { sendTestEmail } = await import('../services/email');

    // Send actual test email
    const success = await sendTestEmail(email, providerId);

    if (success) {
      res.json({
        message: `Test email sent to ${email}`,
        success: true
      });
    } else {
      res.status(500).json({
        message: 'Failed to send test email. Please check your SMTP configuration.',
        success: false
      });
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      message: 'Failed to send test email due to an error',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Payment configuration endpoints
// Ensure trial payment providers exist
function ensureTrialPaymentProvidersExist() {
  console.log('Checking if trial payment providers exist...');
  console.log('Current payment providers:', configStorage.payment.providers.map(p => p.id));
  console.log('Current payment config:', JSON.stringify(configStorage.payment, null, 2));

  // Check if trial-custom-link provider exists
  const trialCustomLinkProviderIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (trialCustomLinkProviderIndex === -1) {
    console.log('Creating trial-custom-link provider...');
    configStorage.payment.providers.push({
      id: 'trial-custom-link',
      name: 'Trial Custom Payment Links',
      active: true,
      config: {
        links: [
          {
            id: 'trial-link-1',
            name: 'Default Trial Payment Link',
            paymentLink: 'https://example.com/pay-trial',
            buttonText: 'Start Trial',
            successRedirectUrl: 'https://example.com/thank-you-trial',
            active: true
          },

          {
            id: 'trial-link-3',
            name: 'Stripe Trial Link',
            paymentLink: 'https://buy.stripe.com/test_trial',
            buttonText: 'Start Trial with Stripe',
            successRedirectUrl: '',
            active: true
          }
        ],
        rotationMethod: 'round-robin',
        lastUsedIndex: 0
      }
    });
  }


}

// Payment configuration endpoints - MOVED TO admin-config.ts for database storage
// adminRouter.get('/payment-config', isAdmin, (req: Request, res: Response) => {
//   // Ensure trial payment providers exist
//   ensureTrialPaymentProvidersExist();
//
//   res.json(configStorage.payment);
// });



adminRouter.post('/payment-config/custom-link', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the Custom Link provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex !== -1) {
    // If this provider is being activated, deactivate all other providers
    if (active) {
      configStorage.payment.providers.forEach((provider, index) => {
        if (provider.id !== 'custom-link') {
          configStorage.payment.providers[index].active = false;
        }
      });
    }

    // Get the current config to preserve links
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((currentConfig as any).links) ? (currentConfig as any).links : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        links,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated Custom Link config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: active
      ? 'Custom Payment Links configuration updated and activated.'
      : 'Custom Payment Links configuration updated successfully',
    config: configStorage.payment
  });
});

// Update Trial Custom Link configuration
adminRouter.post('/payment-config/trial-custom-link', isAdmin, (req: Request, res: Response) => {
  const { active, rotationMethod } = req.body;

  // Find the Trial Custom Link provider to update
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex !== -1) {
    // Get the current config to preserve links
    const currentConfig = configStorage.payment.providers[providerIndex].config || {};
    const links = Array.isArray((currentConfig as any).links) ? (currentConfig as any).links : [];

    // Update the provider config
    configStorage.payment.providers[providerIndex] = {
      ...configStorage.payment.providers[providerIndex],
      active,
      config: {
        links,
        rotationMethod: rotationMethod || 'round-robin',
        lastUsedIndex: (currentConfig as any).lastUsedIndex || 0
      }
    };
  }

  console.log('Updated Trial Custom Link config:',
    JSON.stringify(configStorage.payment.providers[providerIndex], null, 2));

  res.json({
    message: 'Trial Custom Payment Links configuration updated successfully',
    config: configStorage.payment
  });
});

// Create a test sale for testing purposes
adminRouter.post('/create-test-sale', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount } = req.body;

    // Create a test invoice
    const invoiceId = `TEST-${Date.now()}`;
    const invoice = await storage.createInvoice({
      customerName: customerName || 'Test Customer',
      customerEmail: customerEmail || '<EMAIL>',
      productId: productId || 3, // Default to Productivity App Template
      amount: amount || 79.99,
      status: 'draft',
      paypalInvoiceId: invoiceId,
      paypalInvoiceUrl: 'https://www.sandbox.paypal.com/invoice/manage',
      createdAt: new Date().toISOString(),
      notes: 'Test invoice created for demonstration purposes'
    });

    console.log('Created test sale:', invoice);

    res.status(201).json({
      message: 'Test sale created successfully',
      invoiceId,
      invoice
    });
  } catch (error) {
    console.error('Error creating test sale:', error);
    res.status(500).json({
      message: 'Failed to create test sale',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});









// Add a new custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.post('/payment-config/custom-link/add', isAdmin, (req: Request, res: Response) => {
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Pay Now',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new custom payment link:',
    JSON.stringify(links[links.length - 1], null, 2));

  res.json({
    message: 'Custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
});
*/

// Add a new trial custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.post('/payment-config/trial-custom-link/add', isAdmin, (req: Request, res: Response) => {
  console.log('Received request to add trial custom payment link:', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link:',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config:',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add');
});
*/

// Test endpoint
adminRouter.post('/test-endpoint', (req: Request, res: Response) => {
  console.log('Test endpoint called with body:', req.body);
  res.json({
    message: 'Test endpoint called successfully',
    receivedData: req.body
  });
});

// Test endpoint for trial links that doesn't require authentication
adminRouter.post('/test-trial-link', (req: Request, res: Response) => {
  console.log('Test trial link endpoint called with body:', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link (test endpoint):',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config (test endpoint):',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add (test endpoint)');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add (test endpoint)');
});

// Alternative endpoint for adding trial custom payment links
adminRouter.post('/payment-config/add-trial-link', isAdmin, (req: Request, res: Response) => {
  console.log('Received request to add trial custom payment link (alternative endpoint):', req.body);

  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Ensure trial payment providers exist
  ensureTrialPaymentProvidersExist();

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  console.log('Trial custom link provider index:', providerIndex);

  if (providerIndex === -1) {
    console.error('Trial custom payment link provider not found');
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Generate a unique ID for the new link
  const linkId = `trial-link-${Date.now()}`;

  // Add the new link
  links.push({
    id: linkId,
    name: name || `Trial Payment Link ${links.length + 1}`,
    paymentLink: paymentLink || '',
    buttonText: buttonText || 'Start Trial',
    successRedirectUrl: successRedirectUrl || '',
    active: active !== undefined ? active : true
  });

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Added new trial custom payment link (alternative endpoint):',
    JSON.stringify(links[links.length - 1], null, 2));

  console.log('Updated trial custom link provider config (alternative endpoint):',
    JSON.stringify(configStorage.payment.providers[providerIndex].config, null, 2));

  console.log('Sending response for trial custom payment link add (alternative endpoint)');
  res.json({
    message: 'Trial custom payment link added successfully',
    linkId,
    config: configStorage.payment
  });
  console.log('Response sent for trial custom payment link add (alternative endpoint)');
});

// Update a specific custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.post('/payment-config/custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to update
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Payment link with ID ${id} not found`
    });
  }

  // Update the link
  links[linkIndex] = {
    ...links[linkIndex],
    name: name !== undefined ? name : links[linkIndex].name,
    paymentLink: paymentLink !== undefined ? paymentLink : links[linkIndex].paymentLink,
    buttonText: buttonText !== undefined ? buttonText : links[linkIndex].buttonText,
    successRedirectUrl: successRedirectUrl !== undefined ? successRedirectUrl : links[linkIndex].successRedirectUrl,
    active: active !== undefined ? active : links[linkIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Updated custom payment link:',
    JSON.stringify(links[linkIndex], null, 2));

  res.json({
    message: 'Custom payment link updated successfully',
    config: configStorage.payment
  });
});
*/

// Update a specific trial custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.post('/payment-config/trial-custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to update
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Trial payment link with ID ${id} not found`
    });
  }

  // Update the link
  links[linkIndex] = {
    ...links[linkIndex],
    name: name !== undefined ? name : links[linkIndex].name,
    paymentLink: paymentLink !== undefined ? paymentLink : links[linkIndex].paymentLink,
    buttonText: buttonText !== undefined ? buttonText : links[linkIndex].buttonText,
    successRedirectUrl: successRedirectUrl !== undefined ? successRedirectUrl : links[linkIndex].successRedirectUrl,
    active: active !== undefined ? active : links[linkIndex].active
  };

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Updated trial custom payment link:',
    JSON.stringify(links[linkIndex], null, 2));

  res.json({
    message: 'Trial custom payment link updated successfully',
    config: configStorage.payment
  });
});
*/

// Delete a custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.delete('/payment-config/custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;

  // Find the Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to delete
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Payment link with ID ${id} not found`
    });
  }

  // Remove the link
  links.splice(linkIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Deleted custom payment link with ID:', id);

  res.json({
    message: 'Custom payment link deleted successfully',
    config: configStorage.payment
  });
});
*/

// Delete a trial custom payment link - MOVED TO admin-config.ts for database storage
/*
adminRouter.delete('/payment-config/trial-custom-link/:id', isAdmin, (req: Request, res: Response) => {
  const { id } = req.params;

  // Find the Trial Custom Link provider
  const providerIndex = configStorage.payment.providers.findIndex(p => p.id === 'trial-custom-link');
  if (providerIndex === -1) {
    return res.status(404).json({
      message: 'Trial custom payment link provider not found'
    });
  }

  // Get the current links
  const config = configStorage.payment.providers[providerIndex].config || {};
  const links = Array.isArray((config as any).links) ? (config as any).links : [];

  // Find the link to delete
  const linkIndex = links.findIndex((link: any) => link.id === id);
  if (linkIndex === -1) {
    return res.status(404).json({
      message: `Trial payment link with ID ${id} not found`
    });
  }

  // Remove the link
  links.splice(linkIndex, 1);

  // Update the config
  configStorage.payment.providers[providerIndex].config = {
    ...(config as any),
    links
  };

  console.log('Deleted trial custom payment link with ID:', id);

  res.json({
    message: 'Trial custom payment link deleted successfully',
    config: configStorage.payment
  });
});
*/

adminRouter.post('/payment-config/test', isAdmin, async (req: Request, res: Response) => {
  const { provider, config, testEmail } = req.body;

  if (!testEmail) {
    return res.status(400).json({ message: 'Test email is required' });
  }

  // Different validation based on provider
  if (provider === 'custom-link' || provider === 'trial-custom-link') {
    if (!config || !config.paymentLink || !config.buttonText) {
      return res.status(400).json({ message: 'Payment link and button text are required for Custom Payment Link' });
    }
  } else {
    return res.status(400).json({ message: 'Unsupported payment provider' });
  }

  try {
    if (provider === 'custom-link') {
      // Import the custom link service
      const { testCustomLinkConnection } = await import('../services/custom-link');

      // Test the custom payment link
      console.log('Testing custom payment link with provided configuration...');
      const connectionSuccess = await testCustomLinkConnection(config);

      if (!connectionSuccess) {
        return res.status(400).json({
          message: 'Invalid custom payment link configuration. Please check your settings.',
          success: false
        });
      }

      console.log('Custom payment link test successful.');

      // Save the custom link settings temporarily for this test
      const customLinkProvider = configStorage.payment.providers.find(p => p.id === 'custom-link');
      if (customLinkProvider) {
        // If testing a specific link (from the links array)
        if (config.linkId) {
          // Find the link in the current configuration
          const currentConfig = customLinkProvider.config as any;
          const links = Array.isArray(currentConfig.links) ? currentConfig.links : [];
          const linkIndex = links.findIndex((link: any) => link.id === config.linkId);

          if (linkIndex !== -1) {
            // Update just this link for testing
            links[linkIndex] = {
              ...links[linkIndex],
              paymentLink: config.paymentLink || links[linkIndex].paymentLink,
              buttonText: config.buttonText || links[linkIndex].buttonText,
              successRedirectUrl: config.successRedirectUrl || links[linkIndex].successRedirectUrl,
              active: true
            };

            // Make sure other links are inactive for this test
            links.forEach((link: any, idx: number) => {
              if (idx !== linkIndex) {
                link.active = false;
              }
            });

            customLinkProvider.config = {
              ...currentConfig,
              links,
              lastUsedIndex: linkIndex - 1 // So the test will use this link
            };

            console.log(`Using custom payment link "${links[linkIndex].name}" for test:`, links[linkIndex].paymentLink);
          } else {
            console.log(`Link with ID ${config.linkId} not found, using default configuration`);
          }
        }
        // If testing the entire configuration
        else if (config.links && Array.isArray(config.links)) {
          customLinkProvider.config = {
            ...config
          };
          console.log('Using custom payment links configuration for test');
        }
        // If testing a single link (legacy format)
        else if (config.paymentLink) {
          // Create a temporary link for testing
          customLinkProvider.config = {
            links: [
              {
                id: 'test-link',
                name: 'Test Link',
                paymentLink: config.paymentLink,
                buttonText: config.buttonText || 'Pay Now',
                successRedirectUrl: config.successRedirectUrl || '',
                active: true
              }
            ],
            rotationMethod: 'round-robin',
            lastUsedIndex: -1
          };
          console.log('Using single custom payment link for test:', config.paymentLink);
        }

        customLinkProvider.active = true;
      }
    }

    const testCustomerData = {
      fullName: 'Test Customer',
      email: testEmail,
      productId: 0
    };

    const testProduct = {
      id: 0,
      name: 'Test Product',
      description: 'This is a test product for invoice generation',
      price: '1.00',
      imageUrl: '',
      active: true
    };

    // Generate a test invoice based on the provider
    let invoiceResult;

    if (provider === 'custom-link' || provider === 'trial-custom-link') {
      // Import the custom link service
      const { createCustomPaymentLink } = await import('../services/custom-link');

      // Generate a test custom payment link
      const isTrial = provider === 'trial-custom-link';
      console.log(`Creating ${isTrial ? 'trial' : 'regular'} custom payment link for test...`);

      // If testing a specific link, make sure we use the correct provider
      if (isTrial && config.linkId) {
        // Find the link in the trial custom link provider
        const trialCustomLinkProvider = configStorage.payment.providers.find(p => p.id === 'trial-custom-link');
        if (trialCustomLinkProvider) {
          const trialConfig = trialCustomLinkProvider.config as any;
          const trialLinks = Array.isArray(trialConfig.links) ? trialConfig.links : [];
          const linkIndex = trialLinks.findIndex((link: any) => link.id === config.linkId);

          if (linkIndex !== -1) {
            console.log(`Found trial link with ID ${config.linkId} for testing`);
            trialCustomLinkProvider.active = true;
          } else {
            console.log(`Trial link with ID ${config.linkId} not found`);
          }
        }
      }

      invoiceResult = await createCustomPaymentLink(testCustomerData, testProduct, isTrial);
    } else {
      throw new Error(`Unsupported payment provider: ${provider}`);
    }

    const { id, url, isSimulated, error, isDraft, status, noPayPalAccount } = invoiceResult;

    if (provider === 'custom-link' || provider === 'trial-custom-link') {
      try {
        // Send a test email with the custom payment link
        const { sendInvoiceEmail } = await import('../services/email');
        await sendInvoiceEmail(testCustomerData, testProduct, url, provider);
        console.log(`Test email sent to ${testEmail} with ${provider} payment link`);

        // Return success response
        res.json({
          message: `${provider === 'trial-custom-link' ? 'Trial custom' : 'Custom'} payment link generated successfully. Test email sent to ${testEmail}.`,
          success: true,
          invoiceId: id,
          invoiceUrl: url,
          status: 'pending'
        });
      } catch (emailError) {
        console.error('Error sending test email:', emailError);
        res.json({
          message: `${provider === 'trial-custom-link' ? 'Trial custom' : 'Custom'} payment link generated successfully, but failed to send test email: ${emailError.message}`,
          success: true,
          invoiceId: id,
          invoiceUrl: url,
          status: 'pending',
          emailError: emailError.message
        });
      }
    } else if (noPayPalAccount) {
      res.json({
        message: `Connection to ${provider} API successful, but the test email is not valid.`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        noPayPalAccount: true
      });
    } else if (isSimulated) {
      res.json({
        message: `Connection to ${provider} API successful, but invoice generation failed: ${error}`,
        success: false,
        invoiceId: id,
        invoiceUrl: url,
        isSimulated: true,
        error
      });
    } else if (isDraft) {
      res.json({
        message: `Connection to ${provider} API successful. Invoice created in draft status (${status}).`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        isDraft: true,
        status: status
      });
    } else {
      res.json({
        message: `Connection to ${provider} API successful. Invoice generated and sent.`,
        success: true,
        invoiceId: id,
        invoiceUrl: url,
        status: status
      });
    }
  } catch (error) {
    console.error('Error testing payment provider:', error);
    res.status(500).json({
      message: 'Failed to test payment provider',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Mock image upload endpoint
// In a real app, this would use a file storage service like AWS S3
adminRouter.post('/upload-image', isAdmin, (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Generate a random filename
    const randomId = randomBytes(16).toString('hex');
    const imageType = imageData.split(';')[0].split('/')[1];
    const filename = `product-${randomId}.${imageType}`;

    // In a real app, we would save the image to disk or a cloud storage service here
    // For this demo, we'll just pretend we saved it and return a fake URL

    // Return a success response with the mockup image URL
    res.status(200).json({
      url: `https://images.unsplash.com/photo-${randomId}?w=500&q=80`,
      filename
    });
  } catch (error) {
    console.error('Error processing image upload:', error);
    res.status(500).json({ message: 'Failed to process image upload' });
  }
});

export default adminRouter;