import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';
import { exportAllowedEmailsToExcel } from '../utils/excel-export';

export const allowedEmailsRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Schema for creating a new allowed email
const createAllowedEmailSchema = z.object({
  email: z.string().email('Valid email is required'),
  notes: z.string().optional(),
  lastSubject: z.string().optional(),
  smtpProvider: z.string().optional(),
  lastUpdated: z.string().optional()
});

// Function to sanitize notes - now just returns notes as-is since we don't want to extract/remove anything
function sanitizeNotes(notes: string): string {
  return notes || '';
}

// Schema for bulk import of emails
const bulkImportSchema = z.object({
  emails: z.string().min(1, 'Emails are required')
});

// Get all allowed emails
allowedEmailsRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const emails = await storage.getAllowedEmails();
    res.json(emails);
  } catch (error) {
    console.error('Error fetching allowed emails:', error);
    res.status(500).json({ message: 'Failed to fetch allowed emails' });
  }
});

// Create a new allowed email
allowedEmailsRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = createAllowedEmailSchema.parse(req.body);

    // Check if email already exists
    const emails = await storage.getAllowedEmails();
    const exists = emails.some(e => e.email.toLowerCase() === validatedData.email.toLowerCase());

    if (exists) {
      return res.status(400).json({ message: 'Email already exists' });
    }

    // Sanitize notes before storing
    const sanitizedData = {
      ...validatedData,
      notes: validatedData.notes ? sanitizeNotes(validatedData.notes) : undefined,
      createdAt: new Date().toISOString()
    };

    const email = await storage.createAllowedEmail(sanitizedData);

    res.status(201).json(email);
  } catch (error) {
    console.error('Error creating allowed email:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create allowed email' });
  }
});

// Bulk import emails
allowedEmailsRouter.post('/bulk', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = bulkImportSchema.parse(req.body);

    // Split the emails by newline, comma, or space
    const emailList = validatedData.emails
      .split(/[\n,\s]+/)
      .map(email => email.trim())
      .filter(email => email.length > 0);

    if (emailList.length === 0) {
      return res.status(400).json({ message: 'No valid emails provided' });
    }

    const result = await storage.bulkCreateAllowedEmails(emailList);

    res.status(201).json({
      message: `Successfully imported ${result.success} emails. ${result.failed} failed due to duplicates or invalid format.`,
      success: result.success,
      failed: result.failed
    });
  } catch (error) {
    console.error('Error bulk importing emails:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to import emails' });
  }
});

// Delete an allowed email
allowedEmailsRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }

    const email = await storage.getAllowedEmail(id);

    if (!email) {
      return res.status(404).json({ message: 'Email not found' });
    }

    const deleted = await storage.deleteAllowedEmail(id);

    if (deleted) {
      res.json({ message: 'Email deleted successfully' });
    } else {
      res.status(500).json({ message: 'Failed to delete email' });
    }
  } catch (error) {
    console.error('Error deleting allowed email:', error);
    res.status(500).json({ message: 'Failed to delete email' });
  }
});

// Update an allowed email
allowedEmailsRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID' });
    }

    const validatedData = createAllowedEmailSchema.parse(req.body);
    const email = await storage.updateAllowedEmail(id, validatedData);

    if (!email) {
      return res.status(404).json({ message: 'Email not found' });
    }

    res.json(email);
  } catch (error) {
    console.error('Error updating email:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update email' });
  }
});

// Update or create an allowed email by address
allowedEmailsRouter.post('/update-by-email', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { email, ...updateData } = req.body;

    if (!email || typeof email !== 'string') {
      return res.status(400).json({ message: 'Valid email address is required' });
    }

    // Sanitize notes before storing
    const sanitizedUpdate = {
      ...updateData,
      notes: updateData.notes ? sanitizeNotes(updateData.notes) : updateData.notes,
      lastUpdated: new Date().toISOString()
    };

    const result = await storage.updateOrCreateAllowedEmail(email, sanitizedUpdate);

    res.json(result);
  } catch (error) {
    console.error('Error updating/creating email:', error);
    res.status(500).json({ message: 'Failed to update/create email' });
  }
});

// Check if an email is allowed
allowedEmailsRouter.get('/check/:email', async (req: Request, res: Response) => {
  try {
    const { email } = req.params;
    const isAllowed = await storage.isEmailAllowed(email);

    res.json({ isAllowed });
  } catch (error) {
    console.error('Error checking email:', error);
    res.status(500).json({ message: 'Failed to check email' });
  }
});

// Export allowed emails to Excel
allowedEmailsRouter.get('/export', checkAdmin, async (req: Request, res: Response) => {
  try {
    const emails = await storage.getAllowedEmails();

    // Generate Excel file
    const buffer = await exportAllowedEmailsToExcel(emails);

    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=allowed-emails.xlsx');
    res.setHeader('Content-Length', buffer.length);

    // Send the file
    res.send(buffer);
  } catch (error) {
    console.error('Error exporting emails to Excel:', error);
    res.status(500).json({ message: 'Failed to export emails to Excel' });
  }
});
