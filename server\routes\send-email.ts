import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage-factory';
import { sendCustomEmail } from '../services/email';
import { configStorage } from '../config-storage';

export const sendEmailRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Schema for sending emails
const sendEmailSchema = z.object({
  orderId: z.number().optional(),
  to: z.string().email('Valid email is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  smtpProviderId: z.string().optional(),
  addToAllowedEmails: z.boolean().optional().default(false)
});

// Test endpoint to verify the router is working
sendEmailRouter.get('/test', (req: Request, res: Response) => {
  res.json({ message: 'Send email router is working!' });
});

// Send an email
sendEmailRouter.post('/send', checkAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Received request to send email:', req.body);
    const validatedData = sendEmailSchema.parse(req.body);

    // No longer extracting links, M3U links, usernames, or passwords from email content

    // Get customer data for placeholder replacement and determine SMTP provider
    let customerData = {
      customerName: '',
      customerEmail: validatedData.to,
      country: '',
      orderId: '',
      productName: '',
      appType: '',
      macAddress: ''
    };

    // Determine the SMTP provider to use
    let smtpProviderToUse = validatedData.smtpProviderId;

    if (validatedData.orderId) {
      const order = await storage.getInvoice(validatedData.orderId);
      if (order) {
        customerData = {
          customerName: order.customerName || '',
          customerEmail: order.customerEmail || validatedData.to,
          country: order.country || '',
          orderId: order.id?.toString() || '',
          productName: '', // Will be filled from product data
          appType: order.appType || '',
          macAddress: order.macAddress || ''
        };

        // Get product name
        const product = await storage.getProduct(order.productId);
        if (product) {
          customerData.productName = product.name;
        }

        // If no SMTP provider specified in request, try to get it from the checkout page
        if (!smtpProviderToUse && order.customCheckoutPageId) {
          const checkoutPages = await storage.getCustomCheckoutPages();
          const checkoutPage = checkoutPages.find(page => page.id === order.customCheckoutPageId);
          if (checkoutPage && checkoutPage.smtpProviderId) {
            smtpProviderToUse = checkoutPage.smtpProviderId;
            console.log(`Using SMTP provider from checkout page: ${smtpProviderToUse}`);
          }
        }
      }
    }

    // Replace placeholders in subject and content (same logic as Telegram bot)
    let processedSubject = validatedData.subject;
    let processedContent = validatedData.content;

    // Replace customer placeholders
    processedSubject = processedSubject.replace(/\{\{customerName\}\}/g, customerData.customerName);
    processedSubject = processedSubject.replace(/\{\{customerEmail\}\}/g, customerData.customerEmail);
    processedSubject = processedSubject.replace(/\{\{orderId\}\}/g, customerData.orderId);

    processedContent = processedContent.replace(/\{\{customerName\}\}/g, customerData.customerName);
    processedContent = processedContent.replace(/\{\{customerEmail\}\}/g, customerData.customerEmail);
    processedContent = processedContent.replace(/\{\{orderId\}\}/g, customerData.orderId);
    processedContent = processedContent.replace(/\{\{country\}\}/g, customerData.country);
    processedContent = processedContent.replace(/\{\{productName\}\}/g, customerData.productName);
    processedContent = processedContent.replace(/\{\{appType\}\}/g, customerData.appType);
    processedContent = processedContent.replace(/\{\{macAddress\}\}/g, customerData.macAddress);

    // Replace date placeholders
    const currentDate = new Date();
    processedSubject = processedSubject.replace(/\{\{currentYear\}\}/g, currentDate.getFullYear().toString());
    processedSubject = processedSubject.replace(/\{\{date\}\}/g, currentDate.toLocaleDateString());
    processedContent = processedContent.replace(/\{\{currentYear\}\}/g, currentDate.getFullYear().toString());
    processedContent = processedContent.replace(/\{\{date\}\}/g, currentDate.toLocaleDateString());

    console.log('Sending email to:', validatedData.to);
    console.log('Original subject:', validatedData.subject);
    console.log('Processed subject:', processedSubject);
    console.log('Customer data used for replacement:', customerData);
    console.log('SMTP Provider to use:', smtpProviderToUse || 'default');

    let emailResult;
    try {
      // Send the email with processed content using the determined SMTP provider
      emailResult = await sendCustomEmail(
        validatedData.to,
        processedSubject,
        processedContent,
        smtpProviderToUse
      );

      console.log('Email sent successfully:', emailResult);
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      throw new Error(`Failed to send email: ${emailError.message}`);
    }

    // Update the order with the email sent information if orderId is provided
    if (validatedData.orderId) {
      const order = await storage.getInvoice(validatedData.orderId);

      if (order) {
        const notes = order.notes
          ? `${order.notes}\n\nEmail sent on ${new Date().toISOString()} - Subject: ${processedSubject}`
          : `Email sent on ${new Date().toISOString()} - Subject: ${processedSubject}`;

        await storage.updateInvoice(validatedData.orderId, { notes });
      }
    }

    // Add or update the email in the allowed emails list if requested
    let allowedEmailResult = null;
    if (validatedData.addToAllowedEmails) {
      try {
        // Use the customer data we already gathered for placeholder replacement
        let orderDetails = {
          customerName: customerData.customerName,
          customerCountry: customerData.country,
          appType: customerData.appType,
          productName: customerData.productName,
          checkoutPageTitle: '',
          orderDate: ''
        };

        // Get additional order details if available
        if (validatedData.orderId) {
          const order = await storage.getInvoice(validatedData.orderId);
          if (order) {
            orderDetails.orderDate = new Date(order.createdAt).toLocaleDateString();

            // Get checkout page info
            if (order.customCheckoutPageId) {
              const checkoutPages = await storage.getCustomCheckoutPages();
              const checkoutPage = checkoutPages.find(page => page.id === order.customCheckoutPageId);
              if (checkoutPage) {
                orderDetails.checkoutPageTitle = checkoutPage.title;
              }
            }
          }
        }

        // Get the SMTP provider name if available
        let smtpProviderName = '';
        let smtpProviderId = smtpProviderToUse || '';

        if (smtpProviderId) {
          const provider = configStorage.email.providers.find(p => p.id === smtpProviderId);
          if (provider) {
            smtpProviderName = provider.name;
          }
        }

        // Prepare comprehensive notes with all available data
        let notes = '';

        if (orderDetails.customerName) {
          notes += `Customer: ${orderDetails.customerName}\n`;
        }

        if (orderDetails.customerCountry) {
          notes += `Country: ${orderDetails.customerCountry}\n`;
        }

        if (orderDetails.checkoutPageTitle) {
          notes += `Checkout Page: ${orderDetails.checkoutPageTitle}\n`;
        }

        if (orderDetails.appType) {
          notes += `App: ${orderDetails.appType}\n`;
        }

        if (orderDetails.orderDate) {
          notes += `Date: ${orderDetails.orderDate}\n`;
        }

        if (smtpProviderName) {
          notes += `SMTP Provider: ${smtpProviderName}\n`;
        } else if (smtpProviderId) {
          notes += `SMTP Provider: ${smtpProviderId}\n`;
        }

        // Add or update the email in the allowed list
        allowedEmailResult = await storage.updateOrCreateAllowedEmail(validatedData.to, {
          notes: notes.trim(),
          lastSubject: processedSubject,
          smtpProvider: smtpProviderName || smtpProviderId || '',
          lastUpdated: new Date().toISOString()
        });

        console.log('Email added/updated in allowed list:', allowedEmailResult);
      } catch (allowedEmailError) {
        console.error('Error adding email to allowed list:', allowedEmailError);
        // Don't fail the whole request if this part fails
      }
    }

    res.json({
      message: 'Email sent successfully',
      emailResult,
      allowedEmailResult
    });
  } catch (error) {
    console.error('Error sending email:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
