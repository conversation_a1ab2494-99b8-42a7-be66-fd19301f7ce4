import nodemailer from 'nodemailer';
import { Product, CheckoutData } from '../../shared/schema';
import { storage } from '../storage-factory';

/**
 * Create a configured nodemailer transporter based on settings
 * @param providerId Optional SMTP provider ID to use
 * @returns Nodemailer transporter
 */
export async function createTransporter(providerId?: string) {
  const emailConfig = await storage.getEmailConfig();

  // If providerId is specified and not 'default', try to find that specific provider
  if (providerId && providerId !== 'default') {
    const provider = emailConfig.providers.find(p => p.id === providerId && p.active);
    if (provider) {
      console.log(`Using specified SMTP provider: ${provider.name} (${provider.id})`);
      return nodemailer.createTransport({
        host: (provider.credentials as any).host,
        port: parseInt((provider.credentials as any).port),
        secure: (provider.credentials as any).secure,
        auth: {
          user: (provider.credentials as any).auth.user,
          pass: (provider.credentials as any).auth.pass
        }
      });
    } else {
      console.warn(`Specified SMTP provider ${providerId} not found or not active, falling back to default`);
    }
  }

  // Find the default provider
  const defaultProvider = emailConfig.providers.find(p => p.isDefault && p.active);
  if (defaultProvider) {
    console.log(`Using default SMTP provider: ${defaultProvider.name} (${defaultProvider.id})`);
    return nodemailer.createTransport({
      host: (defaultProvider.credentials as any).host,
      port: parseInt((defaultProvider.credentials as any).port),
      secure: (defaultProvider.credentials as any).secure,
      auth: {
        user: (defaultProvider.credentials as any).auth.user,
        pass: (defaultProvider.credentials as any).auth.pass
      }
    });
  }

  // If no default provider, use the first active provider
  const activeProvider = emailConfig.providers.find(p => p.active);
  if (activeProvider) {
    console.log(`Using active SMTP provider: ${activeProvider.name} (${activeProvider.id})`);
    return nodemailer.createTransport({
      host: (activeProvider.credentials as any).host,
      port: parseInt((activeProvider.credentials as any).port),
      secure: (activeProvider.credentials as any).secure,
      auth: {
        user: (activeProvider.credentials as any).auth.user,
        pass: (activeProvider.credentials as any).auth.pass
      }
    });
  }

  // Fallback to hardcoded transporter for testing
  console.warn('No active SMTP providers found, using hardcoded transporter');
  return nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '3d8I9xFm1yMDYj7W'
    }
  });
}

/**
 * Get the backup SMTP transporter if available
 * @returns Nodemailer transporter or null if no backup is available
 */
export async function getBackupTransporter() {
  const emailConfig = await storage.getEmailConfig();

  // Find the backup provider
  const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
  if (backupProvider) {
    console.log(`Using backup SMTP provider: ${backupProvider.name} (${backupProvider.id})`);
    return nodemailer.createTransport({
      host: (backupProvider.credentials as any).host,
      port: parseInt((backupProvider.credentials as any).port),
      secure: (backupProvider.credentials as any).secure,
      auth: {
        user: (backupProvider.credentials as any).auth.user,
        pass: (backupProvider.credentials as any).auth.pass
      }
    });
  }

  return null;
}

// Alias for backward compatibility
export const createEmailTransporter = createTransporter;

/**
 * Send a generic email with text and HTML content
 */
export async function sendEmail(
  emailData: {
    to: string;
    subject: string;
    text?: string;
    html?: string;
  },
  smtpSettings?: any
): Promise<any> {
  try {
    // Create transporter with specific SMTP settings if provided
    let transporter;
    if (smtpSettings) {
      transporter = nodemailer.createTransporter(smtpSettings);
    } else {
      transporter = await createTransporter();
    }

    // Use hardcoded credentials for testing
    const credentials = {
      fromEmail: '<EMAIL>',
      fromName: 'PayPal Invoicer'
    };

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Email sent to ${emailData.to}. Message ID: ${info.messageId}`);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Send a custom email with HTML content using specified SMTP provider
 */
export async function sendCustomEmail(
  to: string,
  subject: string,
  htmlContent: string,
  smtpProviderId?: string
): Promise<any> {
  try {
    // Get the email configuration
    const emailConfig = await storage.getEmailConfig();

    // Get the provider to use
    let provider;

    if (smtpProviderId && smtpProviderId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === smtpProviderId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${smtpProviderId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found, using fallback credentials');
      // Fallback to hardcoded credentials
      const transporter = await createTransporter();
      const credentials = {
        fromEmail: '<EMAIL>',
        fromName: 'PayPal Invoicer'
      };

      const mailOptions = {
        from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
        to,
        subject,
        html: htmlContent
      };

      const info = await transporter.sendMail(mailOptions);
      console.log(`Custom email sent to ${to} with fallback. Message ID: ${info.messageId}`);
      return info;
    }

    // Use the selected provider
    const transporter = await createTransporter(provider.id);
    const credentials = provider.credentials as any;

    console.log(`Sending custom email using SMTP provider: ${provider.name} (${provider.id})`);
    console.log(`From: ${credentials.fromName} <${credentials.fromEmail}>`);

    const mailOptions = {
      from: `"${credentials.fromName}" <${credentials.fromEmail}>`,
      to,
      subject,
      html: htmlContent,
      headers: {
        'X-Mailer': 'PayPal Invoice Generator',
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'Normal'
      }
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Custom email sent to ${to} using ${provider.name}. Message ID: ${info.messageId}`);

    // No longer extracting username/password from email content
    return info;
  } catch (error) {
    console.error('Error sending custom email:', error);
    throw error;
  }
}

/**
 * Send a test email using the configured email provider
 * @param to Recipient email address
 * @param providerId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendTestEmail(to: string, providerId?: string): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the provider to use
    let provider;

    if (providerId && providerId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === providerId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${providerId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    try {
      // Try with the primary SMTP
      const transporter = createTransporter(provider.id);
      if (!transporter) return false;

      const result = await transporter.sendMail({
        from: `"${fromName}" <${fromEmail}>`,
        to,
        subject: `Test Email from PayPal Invoice Generator (${provider.name})`,
        text: `This is a test email from your PayPal Invoice Generator app using the "${provider.name}" SMTP configuration. If you received this, your SMTP settings are working correctly.`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
              <h1 style="margin: 0;">Test Email</h1>
            </div>
            <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
              <p>This is a test email from your PayPal Invoice Generator app using the <strong>"${provider.name}"</strong> SMTP configuration.</p>
              <p>If you're seeing this message, your SMTP settings are working correctly!</p>
              <p>You can now use this email configuration to send invoice notifications to your customers.</p>
              <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                <p><strong>SMTP Configuration:</strong></p>
                <ul>
                  <li>Provider: ${provider.name}</li>
                  <li>Host: ${credentials.host}</li>
                  <li>From: ${fromName} &lt;${fromEmail}&gt;</li>
                </ul>
              </div>
            </div>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
              <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
            </div>
          </div>
        `
      });

      console.log(`Test email sent to ${to} using ${provider.name} (${provider.id}). Message ID: ${result.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending test email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupProvider = emailConfig.providers.find(p => p.isBackup && p.active);
          if (!backupProvider) throw new Error('Backup provider not found');

          const backupCredentials = backupProvider.credentials as any;

          const backupResult = await backupTransporter.sendMail({
            from: `"${backupCredentials.fromName}" <${backupCredentials.fromEmail}>`,
            to,
            subject: `Test Email from PayPal Invoice Generator (Backup: ${backupProvider.name})`,
            text: `This is a test email from your PayPal Invoice Generator app using the BACKUP "${backupProvider.name}" SMTP configuration. If you received this, your backup SMTP settings are working correctly.`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #0070BA; padding: 20px; text-align: center; color: white;">
                  <h1 style="margin: 0;">Test Email (Backup)</h1>
                </div>
                <div style="padding: 20px; border: 1px solid #e9e9e9; border-top: none;">
                  <p>This is a test email from your PayPal Invoice Generator app using the <strong>BACKUP "${backupProvider.name}"</strong> SMTP configuration.</p>
                  <p>If you're seeing this message, your backup SMTP settings are working correctly!</p>
                  <p>The primary SMTP server (${provider.name}) failed, but the backup successfully delivered this email.</p>
                  <div style="background-color: #f8f8f8; padding: 10px; border-left: 4px solid #0070BA; margin-top: 20px;">
                    <p><strong>Backup SMTP Configuration:</strong></p>
                    <ul>
                      <li>Provider: ${backupProvider.name}</li>
                      <li>Host: ${backupCredentials.host}</li>
                      <li>From: ${backupCredentials.fromName} &lt;${backupCredentials.fromEmail}&gt;</li>
                    </ul>
                  </div>
                </div>
                <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 12px; color: #666;">
                  <p>© ${new Date().getFullYear()} PayPal Invoice Generator. All rights reserved.</p>
                </div>
              </div>
            `
          });

          console.log(`Test email sent via backup SMTP ${backupProvider.name} (${backupProvider.id}):`, backupResult.messageId);
          return true;
        } catch (backupError) {
          console.error('Error sending test email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return false;
  }
}

/**
 * Send invoice notification email to the customer
 * @param customerData Customer data
 * @param product Product data
 * @param invoiceUrl Invoice URL or object
 * @param paymentMethod Payment method
 * @param smtpProviderId Optional SMTP provider ID to use
 * @returns Boolean indicating success or failure
 */
export async function sendInvoiceEmail(
  customerData: CheckoutData,
  product: Product,
  invoiceUrl: string | any,
  paymentMethod: string = 'paypal',
  smtpProviderId?: string
): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the provider to use
    let provider;

    if (smtpProviderId && smtpProviderId !== 'default') {
      provider = emailConfig.providers.find(p => p.id === smtpProviderId && p.active);
      if (!provider) {
        console.warn(`Specified SMTP provider ${smtpProviderId} not found or not active, falling back to default`);
      }
    }

    if (!provider) {
      provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                 emailConfig.providers.find(p => p.active);
    }

    if (!provider) {
      console.warn('No active SMTP provider found');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    // Create the transporter
    const transporter = createTransporter(provider.id);
    if (!transporter) {
      console.warn('Email transporter could not be created. Skipping email notification.');
      return false;
    }

    // Get the payment provider config to customize the email
    const paymentConfig = getEmailConfig();

    // Get button text for custom payment link
    let buttonText = 'Complete Payment';
    let paypalButtonHtml = '';

    if (paymentMethod === 'custom-link') {
      // Check if the invoice result has a button text (from the selected link)
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonText) {
        buttonText = invoiceUrl.buttonText;
        // Use the URL from the invoice result
        invoiceUrl = invoiceUrl.url;
      } else {
        // Fallback to default button text
        const customLinkProvider = (await import('../config-storage')).getPaymentConfig().providers.find(p => p.id === 'custom-link');
        if (customLinkProvider && customLinkProvider.config && customLinkProvider.config.links && customLinkProvider.config.links.length > 0) {
          // Use the first active link's button text
          const activeLink = customLinkProvider.config.links.find((link: any) => link.active);
          if (activeLink) {
            buttonText = activeLink.buttonText || buttonText;
          }
        }
      }
    } else if (paymentMethod === 'paypal-button-embed') {
      // Check if the invoice result has button HTML
      if (invoiceUrl && typeof invoiceUrl === 'object' && invoiceUrl.buttonHtml) {
        paypalButtonHtml = invoiceUrl.buttonHtml;
        // Set a placeholder URL for the invoice
        invoiceUrl = '#';
      }
    }

    // Use "Payment Required" for all payment methods
    const emailSubject = 'Payment Required';
    const emailHeading = 'Payment Required';

    // Prepare email content
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: customerData.email,
      subject: emailSubject,
      text: `Hello ${customerData.fullName},\n\nThank you for your order! Your payment for ${product.name.replace(/IPTV/gi, 'Streaming').replace(/iptv/gi, 'streaming')} is ready to be processed.\n\nTo complete your payment, please visit: ${invoiceUrl}\n\nImportant Note: After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.\n\nIf you have any questions, please reply to this email.\n\nThank you,\n${fromName}`,
      headers: {
        'X-Mailer': 'PayPal Invoice Generator',
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'Normal'
      },
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <!-- Header -->
          <div style="background-color: #ffffff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h1 style="color: #333; margin: 0; font-size: 28px; font-weight: bold;">${emailHeading}</h1>
              <p style="color: #666; margin: 10px 0 0 0; font-size: 16px;">Complete your purchase for ${product.name.replace(/IPTV/gi, 'Streaming').replace(/iptv/gi, 'streaming')}</p>
            </div>
          </div>

          <!-- Order Details -->
          <div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
            <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; border-bottom: 2px solid #f0f0f0; padding-bottom: 10px;">Order Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Invoice Number:</td>
                <td style="padding: 8px 0; color: #333;">${paymentMethod === 'custom-link' ?
                  (typeof invoiceUrl === 'string' && invoiceUrl.includes('paymentId=')) ? invoiceUrl.split('paymentId=')[1].split('&')[0] : `INV-${Date.now()}` :
                  paymentMethod === 'paypal-button-embed' ?
                  (typeof invoiceUrl === 'object' && invoiceUrl.id) ? invoiceUrl.id : `INV-${Date.now()}` :
                  (typeof invoiceUrl === 'string' && invoiceUrl.includes('=')) ? invoiceUrl.split('=').pop() : `INV-${Date.now()}`}</td>
              </tr>
              <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Date:</td>
                <td style="padding: 8px 0; color: #333;">${new Date().toLocaleDateString()}</td>
              </tr>
              <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Product:</td>
                <td style="padding: 8px 0; color: #333;">${product.name.replace(/IPTV/gi, 'Streaming').replace(/iptv/gi, 'streaming')}</td>
              </tr>
              <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Description:</td>
                <td style="padding: 8px 0; color: #333;">${(product.description || 'Premium service subscription').replace(/IPTV/gi, 'Streaming').replace(/iptv/gi, 'streaming')}</td>
              </tr>
              <tr style="border-bottom: 1px solid #f0f0f0;">
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Amount:</td>
                <td style="padding: 8px 0; color: #333; font-weight: bold; font-size: 18px;">$${product.price}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Customer:</td>
                <td style="padding: 8px 0; color: #333;">${customerData.fullName}</td>
              </tr>
            </table>
          </div>

          <!-- Important Note in Yellow Box -->
          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
            <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
              <strong>📧 Important Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.
            </p>
          </div>

          <!-- Payment Button -->
          ${paymentMethod === 'paypal-button-embed' ?
            `<div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; margin-bottom: 20px;">
              <p style="margin: 0 0 20px 0; color: #333; font-size: 16px;">Click the button below to complete your payment:</p>
              ${paypalButtonHtml}
            </div>` :
            `<div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; margin-bottom: 20px;">
              <p style="margin: 0 0 20px 0; color: #333; font-size: 16px;">Click the button below to complete your payment:</p>
              <a href="${invoiceUrl}" style="display: inline-block; background-color: #0070BA; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px; box-shadow: 0 2px 4px rgba(0,112,186,0.3);">${buttonText}</a>
            </div>`
          }

          <!-- Footer -->
          <div style="background-color: #ffffff; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
            <p style="margin: 0 0 10px 0; color: #666;">If you have any questions, please reply to this email.</p>
            <p style="margin: 0; color: #333; font-weight: bold;">Thank you,<br>Support Team</p>
          </div>
        </div>
      `
    };

    try {
      // Try with the primary SMTP
      const info = await transporter.sendMail(mailOptions);
      console.log(`Invoice notification email sent to ${customerData.email} using ${provider.name} (${provider.id}). Message ID: ${info.messageId}`);
      return true;
    } catch (primaryError) {
      console.error(`Error sending invoice email via primary SMTP (${provider.id}):`, primaryError);

      // Try with the backup SMTP if available
      const backupTransporter = getBackupTransporter();
      if (backupTransporter) {
        try {
          const backupInfo = await backupTransporter.sendMail(mailOptions);
          console.log(`Invoice notification email sent to ${customerData.email} via backup SMTP. Message ID: ${backupInfo.messageId}`);
          return true;
        } catch (backupError) {
          console.error('Error sending invoice email via backup SMTP:', backupError);
          return false;
        }
      } else {
        return false;
      }
    }
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return false;
  }
}

/**
 * Send admin notification email when a new order is placed from homepage products
 * @param customerData Customer data
 * @param product Product data
 * @param invoice Invoice data
 * @returns Boolean indicating success or failure
 */
export async function sendAdminOrderNotification(
  customerData: CheckoutData,
  product: Product,
  invoice: any
): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the default provider
    const provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                     emailConfig.providers.find(p => p.active);

    if (!provider) {
      console.warn('No active SMTP provider found for admin notification');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    // Create the transporter
    const transporter = createTransporter(provider.id);
    if (!transporter) {
      console.warn('Email transporter could not be created for admin notification');
      return false;
    }

    // Admin email (you can configure this)
    const adminEmail = fromEmail; // Using the same email as from email for now

    const emailSubject = `🔔 New Order Received - Manual Payment Required`;

    // Prepare admin notification email content
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: adminEmail,
      subject: emailSubject,
      headers: {
        'X-Mailer': 'PayPal Invoice Generator',
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'Normal'
      },
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333;">New Order Received</h1>
            <p style="color: #666;">Manual payment processing required</p>
          </div>

          <div style="margin-bottom: 30px;">
            <p><strong>Action Required:</strong> Please send payment details manually to the customer via email.</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333;">Order Details:</h3>
            <p><strong>Order ID:</strong> #${invoice.id}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</p>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Description:</strong> ${product.description || 'Premium service subscription'}</p>
            <p><strong>Amount:</strong> $${product.price}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333;">Customer Information:</h3>
            <p><strong>Name:</strong> ${customerData.fullName}</p>
            <p><strong>Email:</strong> ${customerData.email}</p>
            <p><strong>Country:</strong> ${customerData.country}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h4 style="color: #333;">Next Steps:</h4>
            <ol>
              <li>Review the order details above</li>
              <li>Send payment instructions to <strong>${customerData.email}</strong></li>
              <li>Include your preferred payment method (bank transfer, crypto, etc.)</li>
              <li>Update the order status once payment is received</li>
            </ol>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <p>This notification was sent automatically when a customer placed an order from your homepage products.</p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Admin notification email sent for order #${invoice.id}. Message ID: ${info.messageId}`);
    return true;
  } catch (error) {
    console.error('Error sending admin notification email:', error);
    return false;
  }
}

/**
 * Send customer confirmation email for manual payment processing
 * @param customerData Customer data
 * @param product Product data
 * @returns Boolean indicating success or failure
 */
export async function sendCustomerManualPaymentEmail(
  customerData: CheckoutData,
  product: Product
): Promise<boolean> {
  try {
    // Get the email configuration
    const emailConfig = getEmailConfig();

    // Get the default provider
    const provider = emailConfig.providers.find(p => p.isDefault && p.active) ||
                     emailConfig.providers.find(p => p.active);

    if (!provider) {
      console.warn('No active SMTP provider found for customer confirmation');
      return false;
    }

    const credentials = provider.credentials as any;
    const fromEmail = credentials.fromEmail;
    const fromName = credentials.fromName;

    // Create the transporter
    const transporter = createTransporter(provider.id);
    if (!transporter) {
      console.warn('Email transporter could not be created for customer confirmation');
      return false;
    }

    const emailSubject = `Order Confirmation - ${product.name}`;

    // Prepare customer confirmation email content
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: customerData.email,
      subject: emailSubject,
      headers: {
        'X-Mailer': 'PayPal Invoice Generator',
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'Normal'
      },
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #333;">Order Received!</h1>
            <p style="color: #666;">Thank you for your order, ${customerData.fullName}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #333;">Order Details:</h3>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Product:</strong> ${product.name}</p>
            <p><strong>Description:</strong> ${product.description || 'Premium service subscription'}</p>
            <p><strong>Amount:</strong> $${product.price}</p>
            <p><strong>Customer:</strong> ${customerData.fullName}</p>
            <p><strong>Email:</strong> ${customerData.email}</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h4 style="color: #333;">Payment Instructions Coming Soon</h4>
            <p>We will send you detailed payment instructions via email within <strong>24 hours</strong>. Please check your inbox for our follow-up email with payment details.</p>
          </div>

          <div style="margin-bottom: 30px;">
            <h4 style="color: #333;">What happens next?</h4>
            <ol>
              <li>You'll receive payment instructions via email within 24 hours</li>
              <li>Complete the payment using the provided details</li>
              <li>Your subscription will be activated immediately after payment confirmation</li>
              <li>You'll receive your access details within 8 hours (usually within 3 hours)</li>
            </ol>
          </div>

          <div style="margin-bottom: 30px;">
            <p><strong>Important:</strong> Please keep this email for your records. If you don't receive payment instructions within 24 hours, please reply to this email or contact our support team.</p>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <p>If you have any questions, please reply to this email.</p>
            <p><strong>Thank you for choosing our service!<br>Support Team</strong></p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Customer confirmation email sent to ${customerData.email}. Message ID: ${info.messageId}`);
    return true;
  } catch (error) {
    console.error('Error sending customer confirmation email:', error);
    return false;
  }
}