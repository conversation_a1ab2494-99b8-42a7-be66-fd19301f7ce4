import { pgTable, text as pgText, serial as pgSerial, integer as pgInteger, varchar as pgVarchar, boolean as pgBoolean, numeric as pgNumeric } from "drizzle-orm/pg-core";
import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { mysqlTable, varchar, int, boolean, decimal, text as mysqlText } from "drizzle-orm/mysql-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { SystemMessage } from "./system-messages";

// Determine database type based on the DATABASE_URL
const isSQLite = process.env.DATABASE_URL?.startsWith('sqlite:') ?? false;
const isMySQL = process.env.DATABASE_URL?.startsWith('mysql:') ?? false;

// SQLite, MySQL and PostgreSQL are used directly in the table definitions

export const users = isSQLite
  ? sqliteTable("users", {
      id: integer("id").primaryKey(),
      username: text("username").notNull().unique(),
      password: text("password").notNull(),
    })
  : isMySQL
  ? mysqlTable("users", {
      id: int("id").primaryKey().autoincrement(),
      username: varchar("username", { length: 255 }).notNull().unique(),
      password: varchar("password", { length: 255 }).notNull(),
    })
  : pgTable("users", {
      id: pgSerial("id").primaryKey(),
      username: pgText("username").notNull().unique(),
      password: pgText("password").notNull(),
    });

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Product schema
export const products = isSQLite
  ? sqliteTable("products", {
      id: integer("id").primaryKey(),
      name: text("name").notNull(),
      description: text("description").notNull(),
      price: text("price").notNull(),
      imageUrl: text("image_url").notNull(),
      active: integer("active").notNull().default(1),
      paymentMethod: text("payment_method").notNull().default("custom-link"),
      customPaymentLinkId: text("custom_payment_link_id"),
      trialCustomPaymentLinkId: text("trial_custom_payment_link_id"),
      embedCodeId: text("embed_code_id"),
    })
  : isMySQL
  ? mysqlTable("products", {
      id: int("id").primaryKey().autoincrement(),
      name: varchar("name", { length: 255 }).notNull(),
      description: mysqlText("description").notNull(),
      price: decimal("price", { precision: 10, scale: 2 }).notNull(),
      imageUrl: mysqlText("image_url").notNull(),
      active: boolean("active").notNull().default(true),
      paymentMethod: varchar("payment_method", { length: 50 }).notNull().default("custom-link"),
      customPaymentLinkId: varchar("custom_payment_link_id", { length: 255 }),
      trialCustomPaymentLinkId: varchar("trial_custom_payment_link_id", { length: 255 }),
      embedCodeId: varchar("embed_code_id", { length: 255 }),
    })
  : pgTable("products", {
      id: pgSerial("id").primaryKey(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      description: pgText("description").notNull(),
      price: pgNumeric("price").notNull(),
      imageUrl: pgText("image_url").notNull(),
      active: pgBoolean("active").notNull().default(true),
      paymentMethod: pgVarchar("payment_method", { length: 50 }).notNull().default("custom-link"),
      customPaymentLinkId: pgVarchar("custom_payment_link_id", { length: 255 }),
      trialCustomPaymentLinkId: pgVarchar("trial_custom_payment_link_id", { length: 255 }),
      embedCodeId: pgVarchar("embed_code_id", { length: 255 }),
    });

export const insertProductSchema = createInsertSchema(products).pick({
  name: true,
  description: true,
  price: true,
  imageUrl: true,
  active: true,
  paymentMethod: true,
  customPaymentLinkId: true,
  trialCustomPaymentLinkId: true,
  embedCodeId: true,
});

// Invoice schema
export const invoices = isSQLite
  ? sqliteTable("invoices", {
      id: integer("id").primaryKey(),
      customerName: text("customer_name").notNull(),
      customerEmail: text("customer_email").notNull(),
      productId: integer("product_id").notNull(),
      amount: text("amount").notNull(),
      status: text("status").notNull().default("pending"),
      paypalInvoiceId: text("paypal_invoice_id"),
      paypalInvoiceUrl: text("paypal_invoice_url"),
      isTrialOrder: integer("is_trial_order").notNull().default(0),
      hasUpgraded: integer("has_upgraded").notNull().default(0),
      upgradedAt: text("upgraded_at"),
      createdAt: text("created_at").notNull(),
      customCheckoutPageId: integer("custom_checkout_page_id"),
      country: text("country")
    })
  : isMySQL
  ? mysqlTable("invoices", {
      id: int("id").primaryKey().autoincrement(),
      customerName: varchar("customer_name", { length: 255 }).notNull(),
      customerEmail: varchar("customer_email", { length: 255 }).notNull(),
      productId: int("product_id").notNull(),
      amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
      status: varchar("status", { length: 50 }).notNull().default("pending"),
      paypalInvoiceId: varchar("paypal_invoice_id", { length: 255 }),
      paypalInvoiceUrl: mysqlText("paypal_invoice_url"),
      isTrialOrder: boolean("is_trial_order").notNull().default(false),
      hasUpgraded: boolean("has_upgraded").notNull().default(false),
      upgradedAt: varchar("upgraded_at", { length: 255 }),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      customCheckoutPageId: int("custom_checkout_page_id"),
      country: varchar("country", { length: 255 })
    })
  : pgTable("invoices", {
      id: pgSerial("id").primaryKey(),
      customerName: pgVarchar("customer_name", { length: 255 }).notNull(),
      customerEmail: pgVarchar("customer_email", { length: 255 }).notNull(),
      productId: pgInteger("product_id").notNull(),
      amount: pgNumeric("amount").notNull(),
      status: pgVarchar("status", { length: 50 }).notNull().default("pending"),
      paypalInvoiceId: pgVarchar("paypal_invoice_id", { length: 255 }),
      paypalInvoiceUrl: pgText("paypal_invoice_url"),
      isTrialOrder: pgBoolean("is_trial_order").notNull().default(false),
      hasUpgraded: pgBoolean("has_upgraded").notNull().default(false),
      upgradedAt: pgText("upgraded_at"),
      createdAt: pgText("created_at").notNull(),
      customCheckoutPageId: pgInteger("custom_checkout_page_id"),
      country: pgVarchar("country", { length: 255 })
    });

export const insertInvoiceSchema = createInsertSchema(invoices).pick({
  customerName: true,
  customerEmail: true,
  productId: true,
  amount: true,
  status: true,
  paypalInvoiceId: true,
  paypalInvoiceUrl: true,
  isTrialOrder: true,
  hasUpgraded: true,
  upgradedAt: true,
  createdAt: true,
})
.extend({
  customCheckoutPageId: z.number().optional(),
  country: z.string().optional(),
});

// Import the email validator
import { validateEmailDomain } from '../client/src/lib/email-validator';

// Types
export const checkoutSchema = z.object({
  fullName: z.string().min(2, "Full name is required"),
  email: z.string()
    .email("Please enter a valid email address")
    .refine(
      (email) => validateEmailDomain(email).isValid,
      (email) => ({
        message: validateEmailDomain(email).message || "Email domain not allowed"
      })
    ),
  productId: z.number(),
  country: z.string().min(1, "Country is required"),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect & {
  email?: string;
  rememberMe?: boolean;
  resetToken?: string;
  resetTokenExpiry?: string;
  twoFactorSecret?: string;
  twoFactorEnabled?: boolean;
  recoveryCodes: Array<{ code: string; used: boolean }>;
  devices: Array<{
    id: string;
    name: string;
    ip: string;
    userAgent: string;
    lastLogin: string;
    createdAt: string;
  }>;
};

export type InsertProduct = z.infer<typeof insertProductSchema>;
export type Product = typeof products.$inferSelect;

export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type Invoice = typeof invoices.$inferSelect;

export type CheckoutData = z.infer<typeof checkoutSchema>;

// Custom Checkout Page schema
export const customCheckoutPages = isSQLite
  ? sqliteTable("custom_checkout_pages", {
      id: integer("id").primaryKey(),
      title: text("title").notNull(),
      slug: text("slug").notNull().unique(),
      productName: text("product_name").notNull(),
      productDescription: text("product_description").notNull(),
      price: text("price").notNull(),
      imageUrl: text("image_url"),
      paymentMethod: text("payment_method").notNull(),
      customPaymentLinkId: text("custom_payment_link_id"),
      paypalButtonId: text("paypal_button_id"),
      trialCustomPaymentLinkId: text("trial_custom_payment_link_id"),
      trialPaypalButtonId: text("trial_paypal_button_id"),
      embedCodeId: text("embed_code_id"),
      smtpProviderId: text("smtp_provider_id"),
      requireAllowedEmail: integer("require_allowed_email").notNull().default(0),
      isTrialCheckout: integer("is_trial_checkout").notNull().default(0),
      confirmationMessage: text("confirmation_message"),
      headerTitle: text("header_title"),
      footerText: text("footer_text"),
      headerLogo: text("header_logo"),
      footerLogo: text("footer_logo"),
      themeMode: text("theme_mode").notNull().default("light"),
      useReferrerMasking: integer("use_referrer_masking").notNull().default(0),
      redirectDelay: integer("redirect_delay").notNull().default(2000),
      expiresAt: text("expires_at"),
      active: integer("active").notNull().default(1),
      views: integer("views").notNull().default(0),
      conversions: integer("conversions").notNull().default(0),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("custom_checkout_pages", {
      id: int("id").primaryKey().autoincrement(),
      title: varchar("title", { length: 255 }).notNull(),
      slug: varchar("slug", { length: 255 }).notNull().unique(),
      productName: varchar("product_name", { length: 255 }).notNull(),
      productDescription: mysqlText("product_description").notNull(),
      price: decimal("price", { precision: 10, scale: 2 }).notNull(),
      imageUrl: mysqlText("image_url"),
      paymentMethod: varchar("payment_method", { length: 50 }).notNull(),
      customPaymentLinkId: varchar("custom_payment_link_id", { length: 255 }),
      paypalButtonId: varchar("paypal_button_id", { length: 255 }),
      trialCustomPaymentLinkId: varchar("trial_custom_payment_link_id", { length: 255 }),
      trialPaypalButtonId: varchar("trial_paypal_button_id", { length: 255 }),
      embedCodeId: varchar("embed_code_id", { length: 255 }),
      smtpProviderId: varchar("smtp_provider_id", { length: 255 }),
      requireAllowedEmail: boolean("require_allowed_email").notNull().default(false),
      isTrialCheckout: boolean("is_trial_checkout").notNull().default(false),
      confirmationMessage: mysqlText("confirmation_message"),
      headerTitle: mysqlText("header_title"),
      footerText: mysqlText("footer_text"),
      headerLogo: mysqlText("header_logo"),
      footerLogo: mysqlText("footer_logo"),
      themeMode: varchar("theme_mode", { length: 20 }).notNull().default("light"),
      useReferrerMasking: boolean("use_referrer_masking").notNull().default(false),
      redirectDelay: int("redirect_delay").notNull().default(2000),
      expiresAt: varchar("expires_at", { length: 255 }),
      active: boolean("active").notNull().default(true),
      views: int("views").notNull().default(0),
      conversions: int("conversions").notNull().default(0),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("custom_checkout_pages", {
      id: pgSerial("id").primaryKey(),
      title: pgVarchar("title", { length: 255 }).notNull(),
      slug: pgVarchar("slug", { length: 255 }).notNull().unique(),
      productName: pgVarchar("product_name", { length: 255 }).notNull(),
      productDescription: pgText("product_description").notNull(),
      price: pgNumeric("price").notNull(),
      imageUrl: pgText("image_url"),
      paymentMethod: pgVarchar("payment_method", { length: 50 }).notNull(),
      customPaymentLinkId: pgVarchar("custom_payment_link_id", { length: 255 }),
      paypalButtonId: pgVarchar("paypal_button_id", { length: 255 }),
      trialCustomPaymentLinkId: pgVarchar("trial_custom_payment_link_id", { length: 255 }),
      trialPaypalButtonId: pgVarchar("trial_paypal_button_id", { length: 255 }),
      embedCodeId: pgVarchar("embed_code_id", { length: 255 }),
      smtpProviderId: pgVarchar("smtp_provider_id", { length: 255 }),
      requireAllowedEmail: pgBoolean("require_allowed_email").notNull().default(false),
      isTrialCheckout: pgBoolean("is_trial_checkout").notNull().default(false),
      confirmationMessage: pgText("confirmation_message"),
      headerTitle: pgText("header_title"),
      footerText: pgText("footer_text"),
      headerLogo: pgText("header_logo"),
      footerLogo: pgText("footer_logo"),
      themeMode: pgVarchar("theme_mode", { length: 20 }).notNull().default("light"),
      useReferrerMasking: pgBoolean("use_referrer_masking").notNull().default(false),
      redirectDelay: pgInteger("redirect_delay").notNull().default(2000),
      expiresAt: pgText("expires_at"),
      active: pgBoolean("active").notNull().default(true),
      views: pgInteger("views").notNull().default(0),
      conversions: pgInteger("conversions").notNull().default(0),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertCustomCheckoutPageSchema = createInsertSchema(customCheckoutPages).pick({
  title: true,
  slug: true,
  productName: true,
  productDescription: true,
  price: true,
  imageUrl: true,
  paymentMethod: true,
  customPaymentLinkId: true,
  paypalButtonId: true,
  trialCustomPaymentLinkId: true,
  trialPaypalButtonId: true,
  embedCodeId: true,
  smtpProviderId: true,
  requireAllowedEmail: true,
  isTrialCheckout: true,
  confirmationMessage: true,
  headerTitle: true,
  footerText: true,
  headerLogo: true,
  footerLogo: true,
  themeMode: true,
  useReferrerMasking: true,
  redirectDelay: true,
  expiresAt: true,
  active: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertCustomCheckoutPage = z.infer<typeof insertCustomCheckoutPageSchema>;
export type CustomCheckoutPage = typeof customCheckoutPages.$inferSelect;

// Allowed Emails schema
export const allowedEmails = isSQLite
  ? sqliteTable("allowed_emails", {
      id: integer("id").primaryKey(),
      email: text("email").notNull().unique(),
      notes: text("notes"),
      lastSubject: text("last_subject"),
      smtpProvider: text("smtp_provider"),
      lastUpdated: text("last_updated"),
      createdAt: text("created_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("allowed_emails", {
      id: int("id").primaryKey().autoincrement(),
      email: varchar("email", { length: 255 }).notNull().unique(),
      notes: mysqlText("notes"),
      lastSubject: varchar("last_subject", { length: 255 }),
      smtpProvider: varchar("smtp_provider", { length: 255 }),
      lastUpdated: varchar("last_updated", { length: 255 }),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
    })
  : pgTable("allowed_emails", {
      id: pgSerial("id").primaryKey(),
      email: pgVarchar("email", { length: 255 }).notNull().unique(),
      notes: pgText("notes"),
      lastSubject: pgVarchar("last_subject", { length: 255 }),
      smtpProvider: pgVarchar("smtp_provider", { length: 255 }),
      lastUpdated: pgVarchar("last_updated", { length: 255 }),
      createdAt: pgText("created_at").notNull(),
    });

export const insertAllowedEmailSchema = createInsertSchema(allowedEmails).pick({
  email: true,
  notes: true,
  lastSubject: true,
  smtpProvider: true,
  lastUpdated: true,
  createdAt: true,
});

export type InsertAllowedEmail = z.infer<typeof insertAllowedEmailSchema>;
export type AllowedEmail = typeof allowedEmails.$inferSelect;

// Email Templates schema
export const emailTemplates = isSQLite
  ? sqliteTable("email_templates", {
      id: integer("id").primaryKey(),
      templateId: text("template_id").notNull().unique(),
      name: text("name").notNull(),
      description: text("description"),
      subject: text("subject").notNull(),
      htmlContent: text("html_content").notNull(),
      textContent: text("text_content"),
      content: text("content"), // For backward compatibility
      category: text("category").notNull().default("general"),
      isDefault: integer("is_default").notNull().default(0),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("email_templates", {
      id: int("id").primaryKey().autoincrement(),
      templateId: varchar("template_id", { length: 255 }).notNull().unique(),
      name: varchar("name", { length: 255 }).notNull(),
      description: mysqlText("description"),
      subject: varchar("subject", { length: 255 }).notNull(),
      htmlContent: mysqlText("html_content").notNull(),
      textContent: mysqlText("text_content"),
      content: mysqlText("content"), // For backward compatibility
      category: varchar("category", { length: 50 }).notNull().default("general"),
      isDefault: boolean("is_default").notNull().default(false),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("email_templates", {
      id: pgSerial("id").primaryKey(),
      templateId: pgVarchar("template_id", { length: 255 }).notNull().unique(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      description: pgText("description"),
      subject: pgVarchar("subject", { length: 255 }).notNull(),
      htmlContent: pgText("html_content").notNull(),
      textContent: pgText("text_content"),
      content: pgText("content"), // For backward compatibility
      category: pgVarchar("category", { length: 50 }).notNull().default("general"),
      isDefault: pgBoolean("is_default").notNull().default(false),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertEmailTemplateSchema = isSQLite
  ? z.object({
      templateId: z.string(),
      name: z.string(),
      description: z.string().optional(),
      subject: z.string(),
      htmlContent: z.string(),
      textContent: z.string().optional(),
      content: z.string().optional(),
      category: z.string().default("general"),
      isDefault: z.union([z.boolean(), z.number()]).transform(val => typeof val === 'boolean' ? (val ? 1 : 0) : val),
      createdAt: z.string(),
      updatedAt: z.string(),
    })
  : createInsertSchema(emailTemplates).pick({
      templateId: true,
      name: true,
      description: true,
      subject: true,
      htmlContent: true,
      textContent: true,
      content: true,
      category: true,
      isDefault: true,
      createdAt: true,
      updatedAt: true,
    });

export type InsertEmailTemplate = z.infer<typeof insertEmailTemplateSchema>;
export type EmailTemplate = typeof emailTemplates.$inferSelect;

// PayPal Buttons schema
export const paypalButtons = isSQLite
  ? sqliteTable("paypal_buttons", {
      id: integer("id").primaryKey(),
      name: text("name").notNull(),
      buttonCode: text("button_code").notNull(),
      description: text("description"),
      active: integer("active").notNull().default(1),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("paypal_buttons", {
      id: int("id").primaryKey().autoincrement(),
      name: varchar("name", { length: 255 }).notNull(),
      buttonCode: mysqlText("button_code").notNull(),
      description: mysqlText("description"),
      active: boolean("active").notNull().default(true),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("paypal_buttons", {
      id: pgSerial("id").primaryKey(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      buttonCode: pgText("button_code").notNull(),
      description: pgText("description"),
      active: pgBoolean("active").notNull().default(true),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertPaypalButtonSchema = createInsertSchema(paypalButtons).pick({
  name: true,
  buttonCode: true,
  description: true,
  active: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertPaypalButton = z.infer<typeof insertPaypalButtonSchema>;
export type PaypalButton = typeof paypalButtons.$inferSelect;

// Custom Invoices schema
export const customInvoices = isSQLite
  ? sqliteTable("custom_invoices", {
      id: integer("id").primaryKey(),
      invoiceNumber: text("invoice_number").notNull().unique(),
      customerName: text("customer_name").notNull(),
      customerEmail: text("customer_email").notNull(),
      amount: text("amount").notNull(),
      currency: text("currency").notNull().default("USD"),
      description: text("description").notNull(),
      paypalButtonId: integer("paypal_button_id").notNull(),
      status: text("status").notNull().default("pending"),
      dueDate: text("due_date"),
      viewCount: integer("view_count").notNull().default(0),
      paidAt: text("paid_at"),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("custom_invoices", {
      id: int("id").primaryKey().autoincrement(),
      invoiceNumber: varchar("invoice_number", { length: 255 }).notNull().unique(),
      customerName: varchar("customer_name", { length: 255 }).notNull(),
      customerEmail: varchar("customer_email", { length: 255 }).notNull(),
      amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
      currency: varchar("currency", { length: 10 }).notNull().default("USD"),
      description: mysqlText("description").notNull(),
      paypalButtonId: int("paypal_button_id").notNull(),
      status: varchar("status", { length: 50 }).notNull().default("pending"),
      dueDate: varchar("due_date", { length: 255 }),
      viewCount: int("view_count").notNull().default(0),
      paidAt: varchar("paid_at", { length: 255 }),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("custom_invoices", {
      id: pgSerial("id").primaryKey(),
      invoiceNumber: pgVarchar("invoice_number", { length: 255 }).notNull().unique(),
      customerName: pgVarchar("customer_name", { length: 255 }).notNull(),
      customerEmail: pgVarchar("customer_email", { length: 255 }).notNull(),
      amount: pgNumeric("amount").notNull(),
      currency: pgVarchar("currency", { length: 10 }).notNull().default("USD"),
      description: pgText("description").notNull(),
      paypalButtonId: pgInteger("paypal_button_id").notNull(),
      status: pgVarchar("status", { length: 50 }).notNull().default("pending"),
      dueDate: pgText("due_date"),
      viewCount: pgInteger("view_count").notNull().default(0),
      paidAt: pgText("paid_at"),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertCustomInvoiceSchema = createInsertSchema(customInvoices).pick({
  invoiceNumber: true,
  customerName: true,
  customerEmail: true,
  amount: true,
  currency: true,
  description: true,
  paypalButtonId: true,
  status: true,
  dueDate: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertCustomInvoice = z.infer<typeof insertCustomInvoiceSchema>;
export type CustomInvoice = typeof customInvoices.$inferSelect;

// General Settings schema
export const generalSettings = isSQLite
  ? sqliteTable("general_settings", {
      id: integer("id").primaryKey(),
      siteName: text("site_name").notNull(),
      siteDescription: text("site_description").notNull(),
      logoUrl: text("logo_url"),
      faviconUrl: text("favicon_url"),
      primaryColor: text("primary_color").notNull(),
      secondaryColor: text("secondary_color").notNull(),
      footerText: text("footer_text").notNull(),
      enableCheckout: integer("enable_checkout").notNull().default(1),
      enableCustomCheckout: integer("enable_custom_checkout").notNull().default(1),
      enableTestMode: integer("enable_test_mode").notNull().default(0),
      defaultTestCustomerEnabled: integer("default_test_customer_enabled").notNull().default(0),
      defaultTestCustomerName: text("default_test_customer_name"),
      defaultTestCustomerEmail: text("default_test_customer_email"),
      emailDomainRestrictionEnabled: integer("email_domain_restriction_enabled").notNull().default(0),
      emailDomainRestrictionDomains: text("email_domain_restriction_domains"),
      seoPrivacySettings: text("seo_privacy_settings"), // JSON string
      telegramBotSettings: text("telegram_bot_settings"), // JSON string
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("general_settings", {
      id: int("id").primaryKey().autoincrement(),
      siteName: varchar("site_name", { length: 255 }).notNull(),
      siteDescription: mysqlText("site_description").notNull(),
      logoUrl: varchar("logo_url", { length: 500 }),
      faviconUrl: varchar("favicon_url", { length: 500 }),
      primaryColor: varchar("primary_color", { length: 50 }).notNull(),
      secondaryColor: varchar("secondary_color", { length: 50 }).notNull(),
      footerText: varchar("footer_text", { length: 500 }).notNull(),
      enableCheckout: boolean("enable_checkout").notNull().default(true),
      enableCustomCheckout: boolean("enable_custom_checkout").notNull().default(true),
      enableTestMode: boolean("enable_test_mode").notNull().default(false),
      defaultTestCustomerEnabled: boolean("default_test_customer_enabled").notNull().default(false),
      defaultTestCustomerName: varchar("default_test_customer_name", { length: 255 }),
      defaultTestCustomerEmail: varchar("default_test_customer_email", { length: 255 }),
      emailDomainRestrictionEnabled: boolean("email_domain_restriction_enabled").notNull().default(false),
      emailDomainRestrictionDomains: mysqlText("email_domain_restriction_domains"),
      seoPrivacySettings: mysqlText("seo_privacy_settings"),
      telegramBotSettings: mysqlText("telegram_bot_settings"),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("general_settings", {
      id: pgSerial("id").primaryKey(),
      siteName: pgVarchar("site_name", { length: 255 }).notNull(),
      siteDescription: pgText("site_description").notNull(),
      logoUrl: pgVarchar("logo_url", { length: 500 }),
      faviconUrl: pgVarchar("favicon_url", { length: 500 }),
      primaryColor: pgVarchar("primary_color", { length: 50 }).notNull(),
      secondaryColor: pgVarchar("secondary_color", { length: 50 }).notNull(),
      footerText: pgVarchar("footer_text", { length: 500 }).notNull(),
      enableCheckout: pgBoolean("enable_checkout").notNull().default(true),
      enableCustomCheckout: pgBoolean("enable_custom_checkout").notNull().default(true),
      enableTestMode: pgBoolean("enable_test_mode").notNull().default(false),
      defaultTestCustomerEnabled: pgBoolean("default_test_customer_enabled").notNull().default(false),
      defaultTestCustomerName: pgVarchar("default_test_customer_name", { length: 255 }),
      defaultTestCustomerEmail: pgVarchar("default_test_customer_email", { length: 255 }),
      emailDomainRestrictionEnabled: pgBoolean("email_domain_restriction_enabled").notNull().default(false),
      emailDomainRestrictionDomains: pgText("email_domain_restriction_domains"),
      seoPrivacySettings: pgText("seo_privacy_settings"),
      telegramBotSettings: pgText("telegram_bot_settings"),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

// Homepage Configuration schema
export const homepageConfig = isSQLite
  ? sqliteTable("homepage_config", {
      id: integer("id").primaryKey(),
      sectionsData: text("sections_data").notNull(), // JSON string
      seoSettings: text("seo_settings").notNull(), // JSON string
      themeSettings: text("theme_settings").notNull(), // JSON string
      version: integer("version").notNull().default(1),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("homepage_config", {
      id: int("id").primaryKey().autoincrement(),
      sectionsData: mysqlText("sections_data").notNull(),
      seoSettings: mysqlText("seo_settings").notNull(),
      themeSettings: mysqlText("theme_settings").notNull(),
      version: int("version").notNull().default(1),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("homepage_config", {
      id: pgSerial("id").primaryKey(),
      sectionsData: pgText("sections_data").notNull(),
      seoSettings: pgText("seo_settings").notNull(),
      themeSettings: pgText("theme_settings").notNull(),
      version: pgInteger("version").notNull().default(1),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

// System Messages schema
export const systemMessages = isSQLite
  ? sqliteTable("system_messages", {
      id: integer("id").primaryKey(),
      messageId: text("message_id").notNull().unique(),
      category: text("category").notNull(),
      name: text("name").notNull(),
      description: text("description"),
      content: text("content").notNull(),
      isHtml: integer("is_html").notNull().default(0),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("system_messages", {
      id: int("id").primaryKey().autoincrement(),
      messageId: varchar("message_id", { length: 255 }).notNull().unique(),
      category: varchar("category", { length: 50 }).notNull(),
      name: varchar("name", { length: 255 }).notNull(),
      description: mysqlText("description"),
      content: mysqlText("content").notNull(),
      isHtml: boolean("is_html").notNull().default(false),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("system_messages", {
      id: pgSerial("id").primaryKey(),
      messageId: pgVarchar("message_id", { length: 255 }).notNull().unique(),
      category: pgVarchar("category", { length: 50 }).notNull(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      description: pgText("description"),
      content: pgText("content").notNull(),
      isHtml: pgBoolean("is_html").notNull().default(false),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertSystemMessageSchema = createInsertSchema(systemMessages).pick({
  messageId: true,
  category: true,
  name: true,
  description: true,
  content: true,
  isHtml: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertSystemMessage = z.infer<typeof insertSystemMessageSchema>;
export type SystemMessageRecord = typeof systemMessages.$inferSelect;

// General Settings schemas
export const insertGeneralSettingsSchema = createInsertSchema(generalSettings).pick({
  siteName: true,
  siteDescription: true,
  logoUrl: true,
  faviconUrl: true,
  primaryColor: true,
  secondaryColor: true,
  footerText: true,
  enableCheckout: true,
  enableCustomCheckout: true,
  enableTestMode: true,
  defaultTestCustomerEnabled: true,
  defaultTestCustomerName: true,
  defaultTestCustomerEmail: true,
  emailDomainRestrictionEnabled: true,
  emailDomainRestrictionDomains: true,
  seoPrivacySettings: true,
  telegramBotSettings: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertGeneralSettings = z.infer<typeof insertGeneralSettingsSchema>;
export type GeneralSettingsRecord = typeof generalSettings.$inferSelect;

// Homepage Configuration schemas
export const insertHomepageConfigSchema = createInsertSchema(homepageConfig).pick({
  sectionsData: true,
  seoSettings: true,
  themeSettings: true,
  version: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertHomepageConfig = z.infer<typeof insertHomepageConfigSchema>;
export type HomepageConfigRecord = typeof homepageConfig.$inferSelect;

// SMTP Providers schema
export const smtpProviders = isSQLite
  ? sqliteTable("smtp_providers", {
      id: text("id").primaryKey(),
      name: text("name").notNull(),
      host: text("host").notNull(),
      port: text("port").notNull(),
      secure: integer("secure").notNull().default(0),
      authUser: text("auth_user").notNull(),
      authPass: text("auth_pass").notNull(),
      fromEmail: text("from_email").notNull(),
      fromName: text("from_name").notNull(),
      active: integer("active").notNull().default(1),
      isDefault: integer("is_default").notNull().default(0),
      isBackup: integer("is_backup").notNull().default(0),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("smtp_providers", {
      id: varchar("id", { length: 255 }).primaryKey(),
      name: varchar("name", { length: 255 }).notNull(),
      host: varchar("host", { length: 255 }).notNull(),
      port: varchar("port", { length: 10 }).notNull(),
      secure: boolean("secure").notNull().default(false),
      authUser: varchar("auth_user", { length: 255 }).notNull(),
      authPass: varchar("auth_pass", { length: 255 }).notNull(),
      fromEmail: varchar("from_email", { length: 255 }).notNull(),
      fromName: varchar("from_name", { length: 255 }).notNull(),
      active: boolean("active").notNull().default(true),
      isDefault: boolean("is_default").notNull().default(false),
      isBackup: boolean("is_backup").notNull().default(false),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("smtp_providers", {
      id: pgVarchar("id", { length: 255 }).primaryKey(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      host: pgVarchar("host", { length: 255 }).notNull(),
      port: pgVarchar("port", { length: 10 }).notNull(),
      secure: pgBoolean("secure").notNull().default(false),
      authUser: pgVarchar("auth_user", { length: 255 }).notNull(),
      authPass: pgVarchar("auth_pass", { length: 255 }).notNull(),
      fromEmail: pgVarchar("from_email", { length: 255 }).notNull(),
      fromName: pgVarchar("from_name", { length: 255 }).notNull(),
      active: pgBoolean("active").notNull().default(true),
      isDefault: pgBoolean("is_default").notNull().default(false),
      isBackup: pgBoolean("is_backup").notNull().default(false),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertSmtpProviderSchema = createInsertSchema(smtpProviders).pick({
  id: true,
  name: true,
  host: true,
  port: true,
  secure: true,
  authUser: true,
  authPass: true,
  fromEmail: true,
  fromName: true,
  active: true,
  isDefault: true,
  isBackup: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertSmtpProvider = z.infer<typeof insertSmtpProviderSchema>;
export type SmtpProvider = typeof smtpProviders.$inferSelect;

// Custom Payment Links schema
export const customPaymentLinks = isSQLite
  ? sqliteTable("custom_payment_links", {
      id: text("id").primaryKey(),
      name: text("name").notNull(),
      paymentLink: text("payment_link").notNull(),
      buttonText: text("button_text").notNull(),
      successRedirectUrl: text("success_redirect_url"),
      active: integer("active").notNull().default(1),
      isTrialLink: integer("is_trial_link").notNull().default(0),
      createdAt: text("created_at").notNull(),
      updatedAt: text("updated_at").notNull(),
    })
  : isMySQL
  ? mysqlTable("custom_payment_links", {
      id: varchar("id", { length: 255 }).primaryKey(),
      name: varchar("name", { length: 255 }).notNull(),
      paymentLink: mysqlText("payment_link").notNull(),
      buttonText: varchar("button_text", { length: 255 }).notNull(),
      successRedirectUrl: mysqlText("success_redirect_url"),
      active: boolean("active").notNull().default(true),
      isTrialLink: boolean("is_trial_link").notNull().default(false),
      createdAt: varchar("created_at", { length: 255 }).notNull(),
      updatedAt: varchar("updated_at", { length: 255 }).notNull(),
    })
  : pgTable("custom_payment_links", {
      id: pgVarchar("id", { length: 255 }).primaryKey(),
      name: pgVarchar("name", { length: 255 }).notNull(),
      paymentLink: pgText("payment_link").notNull(),
      buttonText: pgVarchar("button_text", { length: 255 }).notNull(),
      successRedirectUrl: pgText("success_redirect_url"),
      active: pgBoolean("active").notNull().default(true),
      isTrialLink: pgBoolean("is_trial_link").notNull().default(false),
      createdAt: pgText("created_at").notNull(),
      updatedAt: pgText("updated_at").notNull(),
    });

export const insertCustomPaymentLinkSchema = createInsertSchema(customPaymentLinks).pick({
  id: true,
  name: true,
  paymentLink: true,
  buttonText: true,
  successRedirectUrl: true,
  active: true,
  isTrialLink: true,
  createdAt: true,
  updatedAt: true,
});

export type InsertCustomPaymentLink = z.infer<typeof insertCustomPaymentLinkSchema>;
export type CustomPaymentLink = typeof customPaymentLinks.$inferSelect;

